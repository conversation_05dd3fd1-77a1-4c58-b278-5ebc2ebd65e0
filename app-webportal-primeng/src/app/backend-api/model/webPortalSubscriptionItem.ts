/**
 * billing-solution frontend
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */
import { TaxInformation } from './taxInformation';
import { ItemCategory } from './itemCategory';
import { DebitCreditIndicator } from './debitCreditIndicator';
import { Currency } from './currency';
import { Property } from './property';


export interface WebPortalSubscriptionItem { 
    /**
     * The key of the subscription item
     */
    key: string;
    /**
     * The version of the subscription item
     */
    version: number;
    /**
     * The article number of the subscription item
     */
    articleNumber: string;
    /**
     * The item group of the subscription item
     */
    itemGroup?: string;
    category: ItemCategory;
    debitCreditIndicator: DebitCreditIndicator;
    /**
     * The supplier key of the subscription item
     */
    supplierKey?: string;
    /**
     * The name of the subscription item
     */
    name: string;
    /**
     * The description of the subscription item
     */
    description?: string;
    /**
     * The quantity of the subscription item
     */
    quantity: number;
    /**
     * The unit net amount of the subscription item
     */
    unitNetAmount: number;
    /**
     * The total net amount of the subscription item
     */
    totalNetAmount?: number;
    currency: Currency;
    properties?: Array<Property>;
    taxInformation: TaxInformation;
}
export namespace WebPortalSubscriptionItem {
}


