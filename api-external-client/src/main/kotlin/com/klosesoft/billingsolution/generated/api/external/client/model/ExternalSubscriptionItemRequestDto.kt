/**
 *
 * Please note:
 * This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * Do not edit this file manually.
 *
 */

@file:Suppress(
    "ArrayInDataClass",
    "EnumEntryName",
    "RemoveRedundantQualifierName",
    "UnusedImport"
)

package com.klosesoft.billingsolution.generated.api.external.client.model

import com.klosesoft.billingsolution.generated.api.external.client.model.CurrencyDto
import com.klosesoft.billingsolution.generated.api.external.client.model.DebitCreditIndicatorDto
import com.klosesoft.billingsolution.generated.api.external.client.model.ItemCategoryDto
import com.klosesoft.billingsolution.generated.api.external.client.model.PropertyDto
import com.klosesoft.billingsolution.generated.api.external.client.model.TaxInformationDto

import com.fasterxml.jackson.annotation.JsonProperty

/**
 * 
 *
 * @param key The key of the subscription item
 * @param articleNumber The article number of the subscription item
 * @param category 
 * @param debitCreditIndicator 
 * @param name The name of the subscription item
 * @param quantity The quantity of the subscription item
 * @param unitNetAmount The unit price amount of the subscription item
 * @param currency 
 * @param itemGroup The item group of the subscription item
 * @param description The description of the subscription item
 * @param properties 
 * @param taxes 
 */


data class ExternalSubscriptionItemRequestDto (

    /* The key of the subscription item */
    @get:JsonProperty("key")
    val key: kotlin.String,

    /* The article number of the subscription item */
    @get:JsonProperty("articleNumber")
    val articleNumber: kotlin.String,

    @get:JsonProperty("category")
    val category: ItemCategoryDto,

    @get:JsonProperty("debitCreditIndicator")
    val debitCreditIndicator: DebitCreditIndicatorDto,

    /* The name of the subscription item */
    @get:JsonProperty("name")
    val name: kotlin.String,

    /* The quantity of the subscription item */
    @get:JsonProperty("quantity")
    val quantity: java.math.BigDecimal,

    /* The unit price amount of the subscription item */
    @get:JsonProperty("unitNetAmount")
    val unitNetAmount: java.math.BigDecimal,

    @get:JsonProperty("currency")
    val currency: CurrencyDto,

    /* The item group of the subscription item */
    @get:JsonProperty("itemGroup")
    val itemGroup: kotlin.String? = null,

    /* The description of the subscription item */
    @get:JsonProperty("description")
    val description: kotlin.String? = null,

    @get:JsonProperty("properties")
    val properties: kotlin.collections.List<PropertyDto>? = null,

    @get:JsonProperty("taxes")
    val taxes: kotlin.collections.List<TaxInformationDto>? = null

) {


}

