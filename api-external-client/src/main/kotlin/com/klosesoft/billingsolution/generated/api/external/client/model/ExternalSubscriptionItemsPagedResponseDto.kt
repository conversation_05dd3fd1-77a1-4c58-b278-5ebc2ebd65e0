/**
 *
 * Please note:
 * This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * Do not edit this file manually.
 *
 */

@file:Suppress(
    "ArrayInDataClass",
    "EnumEntryName",
    "RemoveRedundantQualifierName",
    "UnusedImport"
)

package com.klosesoft.billingsolution.generated.api.external.client.model

import com.klosesoft.billingsolution.generated.api.external.client.model.ExternalSubscriptionItemResponseDto
import com.klosesoft.billingsolution.generated.api.external.client.model.PageDataDto

import com.fasterxml.jackson.annotation.JsonProperty

/**
 * 
 *
 * @param pageData 
 * @param items 
 */


data class ExternalSubscriptionItemsPagedResponseDto (

    @get:JsonProperty("pageData")
    val pageData: PageDataDto,

    @get:JsonProperty("items")
    val items: kotlin.collections.List<ExternalSubscriptionItemResponseDto>

) {


}

