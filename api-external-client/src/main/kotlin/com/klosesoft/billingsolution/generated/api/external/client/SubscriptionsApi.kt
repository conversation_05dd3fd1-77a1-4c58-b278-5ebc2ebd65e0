package com.klosesoft.billingsolution.generated.api.external.client

import org.openapitools.client.infrastructure.CollectionFormats.*
import retrofit2.http.*
import retrofit2.Response
import okhttp3.RequestBody
import com.fasterxml.jackson.annotation.JsonProperty

import com.klosesoft.billingsolution.generated.api.external.client.model.ExternalSubscriptionDetailsDataDto
import com.klosesoft.billingsolution.generated.api.external.client.model.ExternalSubscriptionDetailsResponseDto
import com.klosesoft.billingsolution.generated.api.external.client.model.ExternalSubscriptionItemRequestDto
import com.klosesoft.billingsolution.generated.api.external.client.model.ExternalSubscriptionItemResponseDto
import com.klosesoft.billingsolution.generated.api.external.client.model.ExternalSubscriptionItemsPagedResponseDto
import com.klosesoft.billingsolution.generated.api.external.client.model.ExternalSubscriptionsPagedResponseDto

interface SubscriptionsApi {
    /**
     * Add a subscription item to a specific subscription
     * 
     * Responses:
     *  - 202: Subscription item creation accepted
     *  - 400: Invalid input
     *  - 404: Subscription not found
     *
     * @param subscriptionKey 
     * @param externalSubscriptionItemRequestDto 
     * @return [Unit]
     */
    @POST("external/v1/subscriptions/{subscription_key}/subscription-items")
    suspend fun addSubscriptionItem(@Path("subscription_key") subscriptionKey: kotlin.String, @Body externalSubscriptionItemRequestDto: ExternalSubscriptionItemRequestDto): Response<Unit>

    /**
     * Create a new subscription
     * 
     * Responses:
     *  - 201: The subscription was successfully created
     *  - 400: Invalid input
     *
     * @param externalSubscriptionDetailsDataDto 
     * @return [ExternalSubscriptionDetailsResponseDto]
     */
    @POST("external/v1/subscriptions")
    suspend fun createSubscription(@Body externalSubscriptionDetailsDataDto: ExternalSubscriptionDetailsDataDto): Response<ExternalSubscriptionDetailsResponseDto>

    /**
     * Delete a specific subscription
     * 
     * Responses:
     *  - 204: Subscription deleted successfully
     *  - 404: Subscription not found
     *
     * @param subscriptionKey 
     * @return [Unit]
     */
    @DELETE("external/v1/subscriptions/{subscription_key}")
    suspend fun deleteSubscription(@Path("subscription_key") subscriptionKey: kotlin.String): Response<Unit>

    /**
     * Delete a subscription item from a specific subscription
     * 
     * Responses:
     *  - 202: Subscription item deletion accepted
     *  - 400: Invalid input
     *  - 404: Subscription or Item not found
     *
     * @param subscriptionKey 
     * @param subscriptionItemKey 
     * @return [Unit]
     */
    @DELETE("external/v1/subscriptions/{subscription_key}/subscription-items/{subscription_item_key}")
    suspend fun deleteSubscriptionItem(@Path("subscription_key") subscriptionKey: kotlin.String, @Path("subscription_item_key") subscriptionItemKey: kotlin.String): Response<Unit>

    /**
     * Get a specific subscription
     * 
     * Responses:
     *  - 200: Successful operation
     *  - 404: Subscription not found
     *
     * @param subscriptionKey 
     * @return [ExternalSubscriptionDetailsResponseDto]
     */
    @GET("external/v1/subscriptions/{subscription_key}")
    suspend fun loadSubscription(@Path("subscription_key") subscriptionKey: kotlin.String): Response<ExternalSubscriptionDetailsResponseDto>

    /**
     * Load subscription item by key from a specific subscription
     * 
     * Responses:
     *  - 200: successful operation
     *  - 404: Subscription or Item not found
     *
     * @param subscriptionKey 
     * @param subscriptionItemKey 
     * @return [ExternalSubscriptionItemResponseDto]
     */
    @GET("external/v1/subscriptions/{subscription_key}/subscription-items/{subscription_item_key}")
    suspend fun loadSubscriptionItem(@Path("subscription_key") subscriptionKey: kotlin.String, @Path("subscription_item_key") subscriptionItemKey: kotlin.String): Response<ExternalSubscriptionItemResponseDto>

    /**
     * Load all subscription items of a specific subscription
     * 
     * Responses:
     *  - 200: Page of subscription items
     *  - 404: Subscription not found
     *
     * @param subscriptionKey 
     * @param page The index of the page to retrieve
     * @param size The number of items per page
     * @param sort The field to sort the subscription items by. Supported values are &#39;createdAt&#39;, &#39;name&#39;, &#39;articleNumber&#39; (optional)
     * @param direction The direction to sort by. Supported values are &#39;asc&#39;, &#39;desc&#39; (optional)
     * @param filter The filter to apply on the subscription items. This can be any attribute of the subscription item. (optional)
     * @return [ExternalSubscriptionItemsPagedResponseDto]
     */
    @GET("external/v1/subscriptions/{subscription_key}/subscription-items")
    suspend fun loadSubscriptionItems(@Path("subscription_key") subscriptionKey: kotlin.String, @Query("page") page: kotlin.Int, @Query("size") size: kotlin.Int, @Query("sort") sort: kotlin.String? = null, @Query("direction") direction: kotlin.String? = null, @Query("filter") filter: kotlin.String? = null): Response<ExternalSubscriptionItemsPagedResponseDto>

    /**
     * Load all subscriptions
     * 
     * Responses:
     *  - 200: Page of subscriptions
     *  - 400: Invalid input
     *
     * @param page The index of the page to retrieve (optional)
     * @param size The number of items per page (optional)
     * @param sort The field to sort the subscriptions by. Supported values are &#39;createdAt&#39;, &#39;name&#39;, &#39;customerKey&#39; (optional)
     * @param direction The direction to sort by. Supported values are &#39;asc&#39;, &#39;desc&#39; (optional)
     * @param filter The filter to apply on the subscriptions. This can be any attribute of the subscription. (optional)
     * @return [ExternalSubscriptionsPagedResponseDto]
     */
    @GET("external/v1/subscriptions")
    suspend fun loadSubscriptions(@Query("page") page: kotlin.Int? = null, @Query("size") size: kotlin.Int? = null, @Query("sort") sort: kotlin.String? = null, @Query("direction") direction: kotlin.String? = null, @Query("filter") filter: kotlin.String? = null): Response<ExternalSubscriptionsPagedResponseDto>

    /**
     * Update a specific subscription
     * 
     * Responses:
     *  - 200: The subscription was successfully updated
     *  - 400: Invalid input
     *  - 404: Subscription not found
     *
     * @param subscriptionKey 
     * @param externalSubscriptionDetailsDataDto 
     * @return [ExternalSubscriptionDetailsResponseDto]
     */
    @PUT("external/v1/subscriptions/{subscription_key}")
    suspend fun updateSubscription(@Path("subscription_key") subscriptionKey: kotlin.String, @Body externalSubscriptionDetailsDataDto: ExternalSubscriptionDetailsDataDto): Response<ExternalSubscriptionDetailsResponseDto>

    /**
     * Update an existing subscription item from a specific subscription
     * 
     * Responses:
     *  - 202: Subscription item update accepted
     *  - 400: Invalid input
     *  - 404: Subscription or Item not found
     *
     * @param subscriptionKey 
     * @param subscriptionItemKey 
     * @param externalSubscriptionItemRequestDto 
     * @return [Unit]
     */
    @PUT("external/v1/subscriptions/{subscription_key}/subscription-items/{subscription_item_key}")
    suspend fun updateSubscriptionItem(@Path("subscription_key") subscriptionKey: kotlin.String, @Path("subscription_item_key") subscriptionItemKey: kotlin.String, @Body externalSubscriptionItemRequestDto: ExternalSubscriptionItemRequestDto): Response<Unit>

}
