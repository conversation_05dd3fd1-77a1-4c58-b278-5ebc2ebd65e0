package com.klosesoft.billingsolution.domain.model.dto

import com.klosesoft.billingsolution.domain.model.valueobject.Currency
import com.klosesoft.billingsolution.domain.model.valueobject.DebitCreditIndicator
import com.klosesoft.billingsolution.domain.model.valueobject.ItemCategory
import com.klosesoft.billingsolution.domain.model.valueobject.PropertyKey
import java.math.BigDecimal
import java.util.EnumMap

data class SubscriptionItemResponseDomainDto(
    val articleNumber: String,
    val debitCreditIndicator: DebitCreditIndicator,
    val category: ItemCategory,
    var itemGroup: String? = null,
    val name: String,
    val description: String? = null,
    val supplierKey: String? = null,
    val quantity: BigDecimal,
    val currency: Currency,
    val unitNetAmount: BigDecimal,
    val totalNetAmount: BigDecimal,
    val taxes: List<TaxInformationDomainDto> = ArrayList(),
    val properties: MutableMap<PropertyKey, String> = EnumMap(PropertyKey::class.java),
    val key: String,
    val version: Long = 0,
)
