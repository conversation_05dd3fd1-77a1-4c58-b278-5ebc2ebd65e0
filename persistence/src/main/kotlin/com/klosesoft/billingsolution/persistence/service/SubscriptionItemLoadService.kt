package com.klosesoft.billingsolution.persistence.service

import com.klosesoft.billingsolution.common.utils.RsqlFieldConstants
import com.klosesoft.billingsolution.common.utils.RsqlUtil
import com.klosesoft.billingsolution.domain.model.ReactivePageImpl
import com.klosesoft.billingsolution.persistence.model.entity.SubscriptionItem
import com.klosesoft.billingsolution.persistence.repository.SubscriptionItemRepository
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.data.domain.Pageable
import org.springframework.data.r2dbc.core.R2dbcEntityTemplate
import org.springframework.stereotype.Service
import java.util.UUID

@Service
class SubscriptionItemLoadService(r2dbcEntityTemplate: R2dbcEntityTemplate) :
    AbstractLoadService<SubscriptionItem>(r2dbcEntityTemplate, SubscriptionItem::class.java) {
    @Autowired
    private lateinit var subscriptionItemRepository: SubscriptionItemRepository

    suspend fun findAllByTenantIdAndSubscriptionId(
        tenantId: UUID,
        subscriptionId: UUID,
        rsqlFilter: String?,
        pageable: Pageable,
    ): ReactivePageImpl<SubscriptionItem> {
        val filter = RsqlUtil.addToFilterWithAnd(rsqlFilter, "${RsqlFieldConstants.SUBSCRIPTION_ID}==$subscriptionId")
        return findAllByTenantId(tenantId, filter, pageable)
    }

    suspend fun findByTenantIdAndSubscriptionIdAndKey(
        tenantId: UUID,
        subscriptionId: UUID,
        subscriptionItemKey: String,
    ): SubscriptionItem = subscriptionItemRepository.findByTenantIdAndSubscriptionIdAndKey(
        tenantId,
        subscriptionId,
        subscriptionItemKey,
    ) ?: throw IllegalArgumentException("SubscriptionItem with key '$subscriptionItemKey' not found")
}
