package com.klosesoft.billingsolution.persistence.repository

import com.klosesoft.billingsolution.persistence.model.entity.SubscriptionItem
import org.springframework.data.repository.kotlin.CoroutineSortingRepository
import org.springframework.stereotype.Repository
import java.util.UUID

@Repository
internal interface SubscriptionItemRepository : CoroutineSortingRepository<SubscriptionItem, UUID> {
    fun findByTenantIdAndSubscriptionIdAndKey(
        tenantId: UUID,
        subscriptionId: UUID,
        subscriptionItemKey: String,
    ): SubscriptionItem?
}
