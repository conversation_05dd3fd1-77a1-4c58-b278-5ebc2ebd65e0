package com.klosesoft.billingsolution.persistence.model.entity

import com.klosesoft.billingsolution.common.utils.TimeProvider
import com.klosesoft.billingsolution.domain.model.valueobject.Currency
import com.klosesoft.billingsolution.domain.model.valueobject.DebitCreditIndicator
import com.klosesoft.billingsolution.domain.model.valueobject.ItemCategory
import com.klosesoft.billingsolution.domain.model.valueobject.PropertyKey
import com.klosesoft.billingsolution.domain.model.valueobject.TaxInformation
import com.klosesoft.billingsolution.persistence.model.entity.base.TenantUpdatableBaseEntity
import org.springframework.data.annotation.Version
import org.springframework.data.relational.core.mapping.Table
import java.math.BigDecimal
import java.time.LocalDateTime
import java.util.EnumMap
import java.util.UUID

@Table(name = "subscription_items")
data class SubscriptionItem(
    val subscriptionId: UUID,
    val articleNumber: String,
    val category: ItemCategory,
    var itemGroup: String? = null,
    val supplierId: UUID? = null,
    val name: String,
    val description: String? = null,
    val quantity: BigDecimal = BigDecimal.ONE,
    val currency: Currency,
    val unitNetAmount: BigDecimal,
    val properties: MutableMap<PropertyKey, String> = EnumMap(PropertyKey::class.java),

    override val debitCreditIndicator: DebitCreditIndicator,
    override val taxes: List<TaxInformation> = ArrayList(),
    override val totalNetAmount: BigDecimal,

    override val key: String,
    override val tenantId: UUID,
    override val id: UUID = UUID.randomUUID(),
    override val createdAt: LocalDateTime = TimeProvider.nowDateTimeInUTC(),
    override val createdBy: UUID,
    override val lastModifiedBy: UUID,
    override val lastModifiedAt: LocalDateTime = TimeProvider.nowDateTimeInUTC(),
    @Version
    override val version: Long = 0,
) : TenantUpdatableBaseEntity,
    ItemWithTaxes
