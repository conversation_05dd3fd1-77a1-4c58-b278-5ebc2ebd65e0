databaseChangeLog:
  - changeSet:
      id: 53
      author: ai-assistant
      changes:
        - createTable:
            tableName: subscription_items
            columns:
              - column:
                  name: id
                  type: uuid
                  constraints:
                    primaryKey: true
                    nullable: false
              - column:
                  name: created_at
                  type: datetime
                  constraints:
                    nullable: false
              - column:
                  name: created_by
                  type: uuid
                  constraints:
                    nullable: false
              - column:
                  name: last_modified_at
                  type: datetime
                  constraints:
                    nullable: false
              - column:
                  name: last_modified_by
                  type: uuid
                  constraints:
                    nullable: false
              - column:
                  name: version
                  type: bigint
                  constraints:
                    nullable: false
              - column:
                  name: tenant_id
                  type: uuid
                  constraints:
                    nullable: false
              - column:
                  name: key
                  type: varchar(100)
                  constraints:
                    nullable: false
              - column:
                  name: subscription_id
                  type: uuid
                  constraints:
                    nullable: false
              - column:
                  name: article_number
                  type: varchar(200)
                  constraints:
                    nullable: false
              - column:
                  name: debit_credit_indicator
                  type: varchar(20)
                  constraints:
                    nullable: false
              - column:
                  name: category
                  type: varchar(30)
                  constraints:
                    nullable: false
              - column:
                  name: item_group
                  type: varchar(200)
                  constraints:
                    nullable: true
              - column:
                  name: name
                  type: varchar(200)
                  constraints:
                    nullable: false
              - column:
                  name: description
                  type: varchar(500)
                  constraints:
                    nullable: true
              - column:
                  name: supplier_id
                  type: uuid
                  constraints:
                    nullable: true
              - column:
                  name: currency
                  type: char(3)
                  constraints:
                    nullable: false
              - column:
                  name: taxes
                  type: jsonb
                  constraints:
                    nullable: false
              - column:
                  name: quantity
                  type: decimal(19, 4)
                  constraints:
                    nullable: false
              - column:
                  name: unit_net_amount
                  type: decimal(19, 4)
                  constraints:
                    nullable: false
              - column:
                  name: total_net_amount
                  type: decimal(19, 4)
                  constraints:
                    nullable: false
              - column:
                  name: properties
                  type: jsonb
                  constraints:
                    nullable: false
        - addUniqueConstraint:
            columnNames: tenant_id,key
            constraintName: tenant_key_unique_subscription_items
            tableName: subscription_items
        - addForeignKeyConstraint:
            baseColumnNames: subscription_id
            baseTableName: subscription_items
            constraintName: fk_subscription_items_subscription_id
            referencedColumnNames: id
            referencedTableName: subscriptions
        - addForeignKeyConstraint:
            baseColumnNames: tenant_id
            baseTableName: subscription_items
            constraintName: fk_subscription_items_tenant_id
            referencedColumnNames: id
            referencedTableName: tenants
        - addForeignKeyConstraint:
            baseColumnNames: supplier_id
            baseTableName: subscription_items
            constraintName: fk_subscription_items_supplier_id
            referencedColumnNames: id
            referencedTableName: suppliers
        - createIndex:
            indexName: idx_subscription_items_subscription_id
            tableName: subscription_items
            columns:
              - column:
                  name: subscription_id
        - createIndex:
            indexName: idx_subscription_items_tenant_id
            tableName: subscription_items
            columns:
              - column:
                  name: tenant_id
