package com.klosesoft.billingsolution.domain.logic.api.service.mapper

import com.klosesoft.billingsolution.domain.model.dto.SubscriptionDomainDto
import com.klosesoft.billingsolution.domain.model.dto.SubscriptionItemResponseDomainDto
import com.klosesoft.billingsolution.domain.model.dto.TaxInformationDomainDto
import com.klosesoft.billingsolution.persistence.model.entity.Subscription
import com.klosesoft.billingsolution.persistence.model.entity.SubscriptionItem
import com.klosesoft.billingsolution.persistence.service.CustomerLoadService
import com.klosesoft.billingsolution.persistence.service.SupplierLoadService
import org.springframework.stereotype.Service

@Service
class SubscriptionDomainApiMapper(
    private val customerLoadService: CustomerLoadService,
    private val supplierLoadService: SupplierLoadService,
) {
    internal suspend fun toSubscriptionDomainDto(
        subscription: Subscription,
    ): SubscriptionDomainDto {
        val customer = customerLoadService.findById(subscription.customerId)

        return SubscriptionDomainDto(
            key = subscription.key,
            name = subscription.name,
            description = subscription.description,
            customerKey = customer.key,
            status = subscription.status,
            frequency = subscription.frequency,
            amount = subscription.amount,
            currency = subscription.currency,
            startDate = subscription.startDate,
            endDate = subscription.endDate,
            nextBillingDate = subscription.nextBillingDate,
            version = subscription.version,
        )
    }

    internal suspend fun toDomainDto(
        subscriptionItem: SubscriptionItem,
    ): SubscriptionItemResponseDomainDto {
        val supplierKey = subscriptionItem.supplierId?.let { supplierId ->
            supplierLoadService.findById(supplierId).key
        }

        return SubscriptionItemResponseDomainDto(
            articleNumber = subscriptionItem.articleNumber,
            debitCreditIndicator = subscriptionItem.debitCreditIndicator,
            category = subscriptionItem.category,
            itemGroup = subscriptionItem.itemGroup,
            name = subscriptionItem.name,
            description = subscriptionItem.description,
            supplierKey = supplierKey,
            quantity = subscriptionItem.quantity,
            currency = subscriptionItem.currency,
            unitNetAmount = subscriptionItem.unitNetAmount,
            totalNetAmount = subscriptionItem.totalNetAmount,
            taxes = subscriptionItem.taxes.map { TaxInformationDomainDto(it.taxType, it.taxRate, it.taxAmount) },
            properties = subscriptionItem.properties,
            key = subscriptionItem.key,
            version = subscriptionItem.version,
        )
    }
}
