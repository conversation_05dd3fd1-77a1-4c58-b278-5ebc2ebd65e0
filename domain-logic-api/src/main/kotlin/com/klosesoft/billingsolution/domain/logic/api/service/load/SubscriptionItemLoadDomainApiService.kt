package com.klosesoft.billingsolution.domain.logic.api.service.load

import com.klosesoft.billingsolution.domain.logic.api.service.mapper.SubscriptionDomainApiMapper
import com.klosesoft.billingsolution.domain.model.ReactivePageImpl
import com.klosesoft.billingsolution.domain.model.dto.SubscriptionItemResponseDomainDto
import com.klosesoft.billingsolution.persistence.service.SubscriptionItemLoadService
import com.klosesoft.billingsolution.persistence.service.SubscriptionLoadService
import com.klosesoft.billingsolution.persistence.service.TenantLoadService
import org.springframework.data.domain.Pageable
import org.springframework.stereotype.Service

@Service
class SubscriptionItemLoadDomainApiService(
    private val subscriptionItemLoadService: SubscriptionItemLoadService,
    private val subscriptionLoadService: SubscriptionLoadService,
    private val subscriptionDomainApiMapper: SubscriptionDomainApiMapper,
    private val tenantLoadService: TenantLoadService,
) {
    suspend fun findSubscriptionItems(
        tenantKey: String,
        subscriptionKey: String,
        rsqlFilter: String?,
        pageable: Pageable,
    ): ReactivePageImpl<SubscriptionItemResponseDomainDto> {
        val tenantId = tenantLoadService.fetchTenantId(tenantKey)
        val subscriptionId = subscriptionLoadService.findByTenantIdAndKey(tenantId, subscriptionKey).id

        return subscriptionItemLoadService.findAllByTenantIdAndSubscriptionId(
            tenantId = tenantId,
            subscriptionId = subscriptionId,
            rsqlFilter = rsqlFilter,
            pageable = pageable,
        ).map { subscriptionDomainApiMapper.toDomainDto(it) }
    }

    suspend fun findSubscriptionItem(
        tenantKey: String,
        subscriptionKey: String,
        subscriptionItemKey: String,
    ): SubscriptionItemResponseDomainDto {
        val tenantId = tenantLoadService.fetchTenantId(tenantKey)
        val subscriptionId = subscriptionLoadService.findByTenantIdAndKey(tenantId, subscriptionKey).id

        val subscriptionItem = subscriptionItemLoadService.findByTenantIdAndSubscriptionIdAndKey(
            tenantId = tenantId,
            subscriptionId = subscriptionId,
            subscriptionItemKey = subscriptionItemKey,
        )

        return subscriptionDomainApiMapper.toDomainDto(subscriptionItem)
    }
}
