{"MAIN": {"USERNAME": "Username", "USERNAME_REQUIRED": "Username is required", "PASSWORD": "Password", "PASSWORD_REQUIRED": "Password is required", "LOGIN": "<PERSON><PERSON>", "CHANGE_PASSWORD": "Change Password", "NEW_PASSWORD": "New Password", "NEW_PASSWORD_REPEAT": "Repeat New Password", "BACK": "Back", "NEW": "New", "EDIT": "Edit", "DELETE": "Delete", "LACKING_RIGHT": "Lacking right", "SAVE": "Save", "YES": "Yes", "NO": "No", "OK": "Ok", "CANCEL": "Cancel", "DETAILS": "Details", "RELOAD": "Reload", "CLOSE": "Close", "APPLY": "Apply", "PROPERTIES": "Properties", "KEY": "Key", "VALUE": "Value", "INPUT_ERROR": "Errors in the form", "SEARCH_REFERENCES": "Search..."}, "MENU": {"DASHBOARD": "Dashboard", "BILLING": "Billing", "CUSTOMERS": "Customers", "SUPPLIERS": "Suppliers", "SUBSCRIPTIONS": "Subscriptions", "ORDER_CAPTURE": "Order Capture", "ORDERS": "Orders", "OPEN_ITEMS": "Open Items", "DOCUMENTS": "Documents", "REPORTS": "Reports", "BUSINESS_SEGMENTS": "Business Segments", "APPROVALS": "Approvals", "PAYMENT": "Payment", "PAYMENT_ACCOUNTS": "Payment Accounts", "PAYMENT_CATEGORIZATION_RULES": "Payment Categorization Rules", "SETTLEMENT_REPORTS": "Settlement Reports", "PAYMENT_TRANSACTIONS": "Payment Transactions", "PAYMENT_ASSIGNMENTS": "Payment Assignments", "BOOKKEEPING": "Bookkeeping", "BOOKING_ACCOUNTS": "Booking Accounts", "BOOKING_RULES": "Booking Rules", "POSTING_RECORDS": "Posting Records", "CURRENCY_EXCHANGE_RATES": "Currency Exchange Rates", "MANUAL_BOOKING": "Manual Booking", "ADMINISTRATION": "Administration", "SENT_ARCHIVE": "Sent Archive", "NOTIFICATIONS": "Notifications", "NOTIFICATION_RECEIVERS": "Notification Receivers", "TRANSLATIONS": "Translations", "REPORT_CONFIGURATIONS": "Report Configurations", "USERS": "Users", "ROLES": "Roles", "WORKFLOW": "Workflow", "INTERNAL": "Internal", "FAILED_JOBS": "Failed Jobs", "HISTORIC_ACTIVITIES": "Historic Activities"}, "VALIDATION": {"REQUIRED": "Field is required", "SPACES_ONLY": "Only spaces are not allowed", "GOA_FACTOR": "Factor must be between 1 and 3.5", "PATTERN": "Invalid format: {{value}}", "NUMERIC": "Invalid number", "FLOAT": "Invalid floating point number", "AMOUNT": "Invalid amount", "MIN_LENGTH": "Minimum length is {{value}}", "MAX_LENGTH": "Maximum length is {{value}}"}, "TABLE": {"NEW": "New", "FINISH": "Finish", "REVERT": "<PERSON><PERSON>", "EDIT": "Edit", "LACKING_RIGHT": "Lacking right {{value}}", "ACTION": "Action", "SHOW_FILTER_PANEL": "Show filter panel", "HIDE_FILTER_PANEL": "Hide filter panel", "FILTERS": "Filters", "CLEAR_ALL": "Clear all", "EXPORT": "Export", "CONFIGURE_COLUMNS": "Configure columns", "EQUAL_TO": "Equal to", "NOT_EQUAL_TO": "Not equal to", "GREATER_THAN": "Greater than", "GREATER_THAN_OR_EQUAL_TO": "Greater than or equal to", "LESS_THAN": "Less than", "LESS_THAN_OR_EQUAL_TO": "Less than or equal to", "NO_DATA_FOR_FILTER": "No data matching the requested filter", "NO_DATA": "No data available", "ITEMS_PER_PAGE": "Items per page:", "NEXT_PAGE": "Next page", "PREVIOUS_PAGE": "Previous page", "FIRST_PAGE": "First page", "LAST_PAGE": "Last page", "OF": "of"}, "DASHBOARD": {"ORDERS": "Orders", "LAST_7_DAYS": "Last 7 days", "REVENUE": "Revenue", "OPEN_TASKS": "Open Tasks", "REPORTS": "Reports", "OPEN_PAYMENT_TRANSACTIONS": "Open Payment Transactions", "OPEN_ITEMS": "Open Items", "OPEN_APPROVALS": "Open Approvals", "NO_OPEN_TASKS": "No open tasks", "NO_REPORTS": "No reports"}, "ADDRESSES": {"STREET": "Street", "HOUSE_NUMBER": "Number", "CITY": "City", "POSTAL_CODE": "Postcode", "MAIL_ADDRESS": "Mail", "PHONE": "Phone", "COUNTRY": "Country", "COMPANY": "Company", "UPDATE_ADDRESS_FAILED": "Failed to update address. Please try again."}, "CUSTOMERS": {"CUSTOMER": "Customer", "COMPANY": "Company", "CUSTOMER_NUMBER": "Customer No.", "SELECT_CUSTOMER": "Select Customer", "TYPE": "Type", "NAME": "Name", "VAT_ID": "Vat-Id", "LANGUAGE": "Language", "BILLING_ADDRESS": "Billing Address", "SHIPPING_ADDRESSES": "Shipping Addresses", "ADD_SHIPPING_ADDRESS": "Add Shipping Address", "DELETE_SHIPPING_ADDRESS": "Delete the shipping address '{{value}}'?", "EDIT_ADDRESS": "Edit Address", "EDIT_CUSTOMER": "Edit Customer", "FIRST_NAME": "First Name", "LAST_NAME": "Last Name", "COMPANY_NAME": "Company Name", "PERSON": "Person", "CUSTOMER_UPDATED": "Customer '{{value}}' updated", "CUSTOMER_DETAILS_BILLING_ADDRESS_UPDATED": "Customer billing address '{{value}}' updated", "CUSTOMER_DETAILS_SHIPPING_ADDRESS_UPDATED": "Customer shipping address '{{value}}' updated", "SHIPPING_ADDRESS_DELETED": "Shipping address '{{value}}' deleted", "SHIPPING_ADDRESS_DELETION_FAILED": "Failed to delete shipping address '{{value}}'", "SHIPPING_ADDRESS_CREATED": "Shipping address '{{value}}' created"}, "SUPPLIERS": {"SUPPLIER": "Supplier", "BILLING_ADDRESS": "Billing Address", "COMPANY_NAME": "Company Name", "NAME": "Name", "VAT_ID": "Vat-Id", "LANGUAGE": "Language", "SUPPLIER_DETAILS_BILLING_ADDRESS_UPDATED": "Supplier billing address '{{value}}' updated", "EDIT_SUPPLIER": "Edit Supplier", "SUPPLIER_UPDATED": "Supplier '{{value}}' updated"}, "ORDER_CAPTURE": {"ORDER_CAPTURE": "Order Capture", "ORDER_CAPTURE_MEDICAL": "Treatment Capture", "ITEMS": "Items", "ARTICLE_NUMBER": "Article Number", "ITEM_NAME": "Item Name", "ITEM_DESCRIPTION": "Item Description", "ITEM_QUANTITY": "Quantity", "ITEM_NET_UNIT_PRICE": "Net Unit Price", "CUSTOMER": "Customer", "CUSTOMER_MEDICAL": "Invoice Recipient", "TAX_RATE": "Tax Rate", "ORDER_CAPTURED": "Order '{{value}}' captured", "ORDER_CAPTURE_FAILED": "Failed to capture order '{{value}}': {{reason}}"}, "NOTES": {"NOTES": "Notes", "NOTE": "Note", "CREATED_AT": "Created at", "CREATED_BY": "Created by", "LAST_MODIFIED_AT": "Last modified at", "LAST_MODIFIED_BY": "Last modified by", "CONTENT": "Content", "EDIT_NOTE": "Edit Note", "NOTE_UPDATED": "Note updated", "NOTE_UPDATE_FAILED": "Failed to update note", "NOTE_DELETED": "Note deleted", "NOTE_DELETION_FAILED": "Failed to delete note", "NOTE_DELETION_CONFIRMATION": "Delete the note?", "NOTE_CREATED": "Note created", "NOTE_CREATION_FAILED": "Failed to create note"}, "ORDERS": {"ORDER_DATE": "Order Date", "ORDER_EDIT": "Order Edit", "EDIT_ITEM": "Order Edit Item", "ORDER_EDIT_MEDICAL": "Treatment Edit", "ORDER_CURRENCY": "Order Currency", "STATUS": "Status", "TOTAL_GROSS_AMOUNT": "Total Gross Amount", "CUSTOMER": "Customer", "ORDER": "Order", "FINALIZE": "Finalize", "CANCEL": "Cancel", "ADDRESS": "Address", "BUSINESS_SEGMENT": "Business Segment", "FINANCING_TYPE": "Financing Type", "BILLING_ADDRESS": "Billing Address", "SHIPPING_ADDRESS": "Shipping Address", "ITEMS": "Order Items", "TOTAL_NET": "Total Net", "TOTAL_TAX": "Total Tax", "TOTAL_GROSS": "Total Gross", "BALANCE_INFORMATION": "Balance Information", "TOTAL_DEBIT": "Total Debit", "TOTAL_CREDIT": "Total Credit", "OPEN_AMOUNT": "Open Amount", "POSTING_RECORDS": "Posting Records", "SUPPLIERS": "Suppliers", "DOCUMENTS": "Documents", "NAME": "Name", "DESCRIPTION": "Description", "ARTICLE_NUMBER": "Article Number", "ITEM_GROUP": "Item Group", "UNIT_PRICE": "Unit Price", "QUANTITY": "Quantity", "NUMBER": "Number", "TARGET": "Target", "BOOKING_DATE": "Booking Date", "REFERENCE": "Reference", "POSTING_DATE": "Posting Date", "TYPE": "Type", "DEBIT_CREDIT": "Debit/Credit", "CATEGORY": "Category", "TOTAL_AMOUNT": "Total Amount", "FINALIZE_ORDER": "Finalize Order", "CONFIRM_FINALIZE_ORDER": "Are you sure you want to finalize the order '{{value}}'?", "CANCEL_ORDER": "Cancel Order", "CONFIRM_CANCEL_ORDER": "Are you sure you want to cancel the order '{{value}}'?", "ORDER_FINALIZED": "Order '{{value}}' finalized", "ORDER_FINALIZATION_REQUESTED": "Order '{{value}}' finalization is requested. Please wait and refresh page.", "ORDER_FINALIZATION_FAILED": "Failed to finalize order '{{value}}'", "ORDER_CANCELLED": "Order '{{value}}' cancelled", "CANCEL_NEEDS_APPROVAL": "Order '{{value}}' cancellation needs approval", "ORDER_CANCELLATION_REQUESTED": "Order '{{value}}' cancellation is requested. Please wait and refresh page.", "ORDER_CANCELLATION_FAILED": "Failed to cancel order '{{value}}'", "DOWNLOAD_FAILED": "Download of document '{{value}}' failed", "HISTORY": "History", "PROCESSING_STATUS": "Processing Status", "CREATED_AT": "Timestamp", "USERNAME": "Username", "ADDITIONAL_INFORMATION": "Additional Information", "LOAD_FAILED": "Load order '{{value}}' failed", "SELECT_ORDER": "Select Order"}, "OPEN_ITEMS": {"ORDER": "Order", "CREATED_AT": "Created At", "STATUS": "Status", "TOTAL_DEBIT": "Total Debit", "TOTAL_CREDIT": "Total Credit", "OPEN_AMOUNT": "Open Amount", "BALANCE_CASE": "Balance Case", "BALANCE_CASE_ITEMS": "Balance Case Items", "DEBIT_CREDIT": "Debit/Credit", "TYPE": "Type", "AMOUNT": "Amount", "POSTING_DATE": "Posting Date", "REFERENCE": "Reference"}, "ORDER_TEMPLATES": {"ORDER_TEMPLATE": "Template", "TEMPLATE_SAVED": "Template '{{value}}' saved", "TEMPLATE_SAVE_FAILED": "Failed to save template '{{value}}'", "NEW_TEMPLATE": "New Template", "OVERWRITE_TEMPLATE": "Overwrite Template", "SAVE_TEMPLATE_AS": "Save As Template", "SELECT_TEMPLATE": "Select Template", "CONFIRM_SAVE_TEMPLATE": "Overwrite template '{{value}}'?", "TITLE": "Title", "DESCRIPTION": "Description", "CREATED_AT": "Created At"}, "DOCUMENTS": {"ORDER": "Order", "DOCUMENT_DATE": "Document Date", "DOCUMENT_TYPE": "Document Type", "PAYMENT_STATUS": "Payment Status", "CREATED_AT": "Created At", "DOWNLOAD_FAILED": "Load document '{{value}}' failed", "DOWNLOAD_JSON": "Download JSON", "DOWNLOAD_XML": "Download XML", "DOWNLOAD_ZUGFERD": "Download ZUGFeRD", "DOWNLOAD_XRECHNUNG": "Download XRechnung", "OPEN_PDF": "Open E-PDF", "OPEN_PDF_COPY": "Open E-PDF Copy", "DOCUMENT": "Document", "LOAD_FAILED": "Load document '{{value}}' failed", "SELECT_DOCUMENT": "Select Document", "TOTAL_AMOUNT": "Total Amount", "ITEMS": "Items", "NAME": "Name", "ARTICLE_NUMBER": "Article Number", "ITEM_GROUP": "Item Group", "UNIT_PRICE": "Unit Price", "QUANTITY": "Quantity", "NUMBER": "Number", "TOTAL_NET": "Total Net", "TOTAL_TAX": "Total Tax", "TOTAL_GROSS": "Total Gross"}, "REPORTS": {"REPORT_TYPE": "Report Type", "REPORT_DATE": "Report Date", "CREATED_AT": "Created At", "DOWNLOAD_FAILED": "Load report '{{value}}' failed"}, "USER_SETTINGS": {"USER_PROFILE": "User Profile", "DETAILS": "Details", "LANGUAGE": "Language", "UPDATED": "User profile updated", "UPDATE_FAILED": "Failed to update user profile"}, "BUSINESS_SEGMENTS": {"BUSINESS_SEGMENT": "Business Segment", "NEW_BUSINESS_SEGMENT": "New Business Segment", "INVOICE_NUMBER_PATTERN": "Invoice Number Pattern", "VAT_ID": "Vat-Id", "LOGO": "Logo", "UPLOAD_NEW_LOGO": "Upload new logo", "BUSINESS_SEGMENT_CREATED": "Business Segment '{{value}}' created", "BUSINESS_SEGMENT_CREATION_FAILED": "Failed to create business segment '{{value}}'", "BUSINESS_SEGMENT_UPDATED": "Business Segment '{{value}}' updated", "BUSINESS_SEGMENT_UPDATE_FAILED": "Failed to update business segment '{{value}}'"}, "APPROVALS": {"APPROVAL_TYPE": "Approval Type", "DESCRIPTION": "Description", "TARGET_TYPE": "Target Type", "TARGET_KEY": "Target", "ORDER": "Order", "PAYMENT_ASSIGNMENT": "Payment Assignment", "EDITOR": "Editor", "STATUS": "Status", "CREATED_AT": "Created At", "APPROVAL": "Approval", "APPROVE": "Approve", "REJECT": "Reject", "AUTHOR": "Author", "REVIEWER": "Reviewer", "COMMENT": "Comment", "SELF_APPROVAL_NOT_ALLOWED": "Self approval is not allowed", "APPROVAL_APPROVED": "Approval '{{value}}' approved", "APPROVAL_APPROVE_FAILED": "Failed to approve approval '{{value}}'", "APPROVAL_REJECTED": "Approval '{{value}}' rejected", "APPROVAL_REJECTION_FAILED": "Failed to reject approval '{{value}}'"}, "PAYMENT_ACCOUNTS": {"ACCOUNT_ID": "Account ID", "ACCOUNT_TYPE": "Account Type", "NAME": "Name", "DESCRIPTION": "Description", "ACCOUNT_HOLDER": "Account Holder", "BIC": "BIC", "DEFAULT_ACCOUNT": "De<PERSON><PERSON> Account", "NEW_PAYMENT_ACCOUNT": "New Payment Account", "PAYMENT_ACCOUNT": "Payment Account", "PAYMENT_ACCOUNT_CREATED": "Payment account '{{value}}' created", "PAYMENT_ACCOUNT_CREATION_FAILED": "Failed to create payment account '{{value}}'", "PAYMENT_ACCOUNT_UPDATED": "Payment account '{{value}}' updated", "PAYMENT_ACCOUNT_UPDATE_FAILED": "Failed to update payment account '{{value}}'"}, "PAYMENT_CATEGORIZATION_RULES": {"NAME": "Name", "PAYMENT_CATEGORY": "Payment Category", "PAYMENT_CATEGORIZATION_RULE": "Payment Categorization Rule", "MATCHING_RULES": "Matching Rules", "SENDERS": "Senders", "RECEIVERS": "Receivers", "PURPOSES": "Purposes", "DEBIT_CREDIT": "Debit/Credit", "TARGET": "Target", "PAYMENT_CATEGORIZATION_RULE_CREATED": "Payment categorization rule '{{value}}' created", "PAYMENT_CATEGORIZATION_RULE_CREATION_FAILED": "Failed to create payment categorization rule '{{value}}'", "PAYMENT_CATEGORIZATION_RULE_UPDATED": "Payment categorization rule '{{value}}' updated", "PAYMENT_CATEGORIZATION_RULE_UPDATE_FAILED": "Failed to update payment categorization rule '{{value}}'", "NEW_PAYMENT_CATEGORIZATION_RULE": "New Payment Categorization Rule"}, "SETTLEMENT_REPORTS": {"ACCOUNT_NUMBER": "Account ID", "CREATED_AT": "Created At", "NEW_SETTLEMENT_REPORT": "New Settlement Report", "SETTLEMENT_REPORT": "Settlement Report", "UPLOAD_A_SETTLEMENT_FILE": "Upload a settlement file", "ORIGIN": "Origin", "FORMAT": "Format", "SETTLEMENT_REPORT_ITEMS": "Settlement Report Items", "DEBIT_CREDIT": "Debit/Credit", "AMOUNT": "Amount", "BOOKING_DATE": "Booking Date", "VALUE_DATE": "Value Date", "RECEIVER": "Receiver", "SENDER": "Sender", "PURPOSE": "Purpose", "SETTLEMENT_REPORT_CREATED": "Settlement Report '{{value}}' created", "SETTLEMENT_REPORT_CREATION_FAILED": "Failed to create settlement report '{{value}}'"}, "PAYMENT_TRANSACTIONS": {"BOOKING_DATE": "Booking Date", "DEBIT_CREDIT": "Debit/Credit", "TOTAL_AMOUNT": "Total Amount", "PAYMENT_CATEGORY": "Payment Category", "ASSIGNMENT_STATUS": "Assigment Status", "PROVIDED_REFERENCE": "Provided Reference", "PAYMENT_TRANSACTION": "Payment Transaction", "PAYMENT_ASSIGNMENT": "Payment Assignment", "PAYMENT_ASSIGNMENTS": "Payment Assignments", "TOTAL": "Total", "ASSIGNED": "Assigned", "OPEN": "Open", "ASSIGNED_ENTITY": "Assigned Entity", "ASSIGNED_DATE": "Assigned At", "ORIGIN": "Origin", "TYPE": "Type", "STATUS": "Status", "AMOUNT": "Amount"}, "PAYMENT_ASSIGNMENTS": {"ASSIGNED_AT": "Assigned At", "PAYMENT_TRANSACTION": "Payment Transaction", "DEBIT_CREDIT": "Debit/Credit", "TARGET_TYPE": "Target Type", "TARGET_KEY": "Target", "TYPE": "Type", "ORIGIN": "Origin", "AMOUNT": "Amount", "NEW_PAYMENT_ASSIGNMENT": "New Payment Assignment", "PAYMENT_ASSIGNMENT": "Payment Assignment", "ASSIGNED_ENTITY": "Assigned Entity", "CURRENCY": "<PERSON><PERSON><PERSON><PERSON>", "SELECT_ORDER": "Select Order", "TOTAL_AMOUNT": "Total Amount", "OPEN_AMOUNT": "Open Amount", "AVAILABLE_AMOUNT": "Available Amount", "ORDER": "Order", "DOCUMENT": "Document", "ORDER_SELECT": "Select Order", "DOCUMENT_SELECT": "Select Document", "ORDER_DATE": "Order Date", "DOCUMENT_DATE": "Document Date", "PAYMENT_ASSIGNMENT_CREATED": "Payment assignment '{{value}}' created", "PAYMENT_ASSIGNMENT_CREATION_FAILED": "Failed to create new payment assignment", "REVERSE": "Reverse", "CONFIRM_REVERSE": "Reverse payment assignment '{{value}}' of order '{{value}}'"}, "BOOKING_ACCOUNTS": {"ACCOUNT_NUMBER": "Account Number", "ACCOUNT_TYPE": "Account Type", "NAME": "Name", "DESCRIPTION": "Description", "NEW_BOOKING_ACCOUNT": "New Booking Account", "BOOKING_ACCOUNT": "Booking Account", "BOOKING_ACCOUNT_CREATED": "Booking account '{{value}}' created", "BOOKING_ACCOUNT_CREATION_FAILED": "Failed to create booking account '{{value}}': {{reason}}", "BOOKING_ACCOUNT_UPDATED": "Booking account '{{value}}' updated", "BOOKING_ACCOUNT_UPDATE_FAILED": "Failed to update booking account '{{value}}': {{reason}}"}, "BOOKING_RULES": {"NAME": "Name", "BOOKING_RULE_TYPE": "Booking Rule Type", "DEBIT_CREDIT": "Debit/Credit", "DEBIT_ACCOUNT_NUMBER": "Debit Account Number", "CREDIT_ACCOUNT_NUMBER": "Credit Account Number", "DEBTOR_ACCOUNT": "(Debtor Account)", "BOOKING_RULE": "Booking Rule", "MATCHING_RULES": "Matching Rules", "ITEM_GROUPS": "Item Groups", "ITEM_NAMES": "Item Names", "ITEM_CATEGORIES": "Item Categories", "ITEM_ARTICLE_NUMBERS": "Item Article Numbers", "TAX_CODES": "Tax Codes", "TARGET_TYPES": "Target Types", "BANK_ACCOUNT_NUMBERS": "Bank Account Numbers", "PAYMENT_CATEGORIES": "Payment Categories", "TARGET_ACCOUNTS": "Target Accounts", "IS_PARTY_ACCOUNT": "Is Party Account", "DEBIT_POSTING_KEY": "Debit Posting Key", "CREDIT_POSTING_KEY": "Credit Posting Key", "NEW_BOOKING_RULE": "New Booking Rule", "BOOKING_RULE_CREATED": "Booking rule '{{value}}' created", "BOOKING_RULE_CREATION_FAILED": "Failed to create booking rule '{{value}}': {{reason}}", "BOOKING_RULE_UPDATED": "Booking rule '{{value}}' updated", "BOOKING_RULE_UPDATE_FAILED": "Failed to update booking rule '{{value}}': {{reason}}"}, "POSTING_RECORDS": {"NUMBER": "Number", "TARGET_TYPE": "Target Type", "TARGET_KEY": "Target", "ORDER": "Order", "DOCUMENT": "Document", "BOOKING_DATE": "Booking Date", "POSTING_RECORD": "Posting Record", "CURRENCY": "<PERSON><PERSON><PERSON><PERSON>", "CREATION_DATE": "Creation Date", "SERVICE_PERIOD": "Service Period", "ENTRIES": "Entries", "POSTING_RECORD_ENTRIES": "Posting Record Entries", "AMOUNT": "Amount", "DEBIT_ACCOUNT": "Debit Account", "DEBIT_POSTING_KEY": "Debit Posting Key", "CREDIT_ACCOUNT": "Credit Account", "CREDIT_POSTING_KEY": "Credit Posting Key", "DOCUMENT_TYPE": "Document Type"}, "CURRENCY_EXCHANGE_RATES": {"VALID_FROM": "<PERSON><PERSON>", "SOURCE_CURRENCY": "Source Currency", "TARGET_CURRENCY": "Target Currency", "RATE": "Rate"}, "MANUAL_BOOKING": {"NEW_MANUAL_BOOKING": "Manual Booking", "NEW_ENTRY": "New Entry", "BOOKING_TEXT": "Booking Text", "DEBIT_ACCOUNT": "Debit Account", "DEBIT_POSTING_KEY": "Debit Posting Key", "CREDIT_ACCOUNT": "Credit Account", "CREDIT_POSTING_KEY": "Credit Posting Key", "MANUAL_BOOKING_CREATED": "Manual booking created", "MANUAL_BOOKING_CREATION_FAILED": "Failed to create manual booking"}, "MONEY_INPUT": {"AMOUNT": "Amount", "CURRENCY": "<PERSON><PERSON><PERSON><PERSON>"}, "SENT_ARCHIVE": {"SENT_AT": "<PERSON><PERSON>", "TYPE": "Type", "TARGET_TYPE": "Target Type", "TARGET_KEY": "Target", "STATUS": "Status", "RESEND": "Resend", "SENT_ARCHIVE_ENTRY": "Sent Archive Entry", "EMAIL_TO": "To", "EMAIL_HEADER": "Subject", "EMAIL_BODY": "Body", "EMAIL_ATTACHMENT_FILENAMES": "Attachment Filenames"}, "NOTIFICATIONS": {"CREATED_AT": "Created At", "SENT_AT": "<PERSON><PERSON>", "TYPE": "Type", "TARGET_TYPE": "Target Type", "TARGET_KEY": "Target", "STATUS": "Status", "RESEND": "Resend"}, "NOTIFICATION_RECEIVERS": {"BASE_URL": "Base URL", "NOTIFICATION_RECEIVER": "Notification Receiver", "AUTHORIZATION": "Authorization", "AUTHORIZATION_TYPE": "Authorization Type", "USERNAME": "Username", "PASSWORD": "Password", "FIXED_API_TOKEN": "Fixed API Token", "CLIENT_ID": "Client ID", "CLIENT_SECRET": "Client Secret", "ACCESS_TOKEN_URI": "Access Token URI", "ROLES": "Roles", "NEW_NOTIFICATION_RECEIVER": "New Notification Receiver", "NOTIFICATION_RECEIVER_CREATED": "Notification receiver '{{value}}' created", "NOTIFICATION_RECEIVER_CREATION_FAILED": "Failed to create notification receiver '{{value}}'", "NOTIFICATION_RECEIVER_UPDATED": "Notification receiver '{{value}}' updated", "NOTIFICATION_RECEIVER_UPDATE_FAILED": "Failed to update notification receiver '{{value}}'", "EVENTS": "Events", "NOTIFICATION_TYPES": "Notification Types", "ADD_SUBPATH": "Add Subpath", "SUBPATH": "Subpath"}, "TRANSLATIONS": {"TRANSLATION_KEY": "Translation Key", "VALUE": "Value", "LANGUAGE": "Language", "TRANSLATION": "Translation", "TRANSLATION_TYPE": "Type", "NEW_TRANSLATION": "New Translation", "TRANSLATION_CREATED": "Translation '{{value}}' created", "TRANSLATION_CREATION_FAILED": "Failed to create translation '{{value}}'", "TRANSLATION_UPDATED": "Translation '{{value}}' updated", "TRANSLATION_UPDATE_FAILED": "Failed to update translation '{{value}}'"}, "REPORT_CONFIGURATIONS": {"REPORT_TYPE": "Report Type", "REPORT_INTERVAL": "Report Interval", "REPORT_FORMAT": "Report Format", "REPORT_CONFIGURATION": "Report Configuration", "TIMEZONE": "Timezone", "NEW_REPORT_CONFIGURATION": "New Report Configuration", "REPORT_CONFIGURATION_CREATED": "Report configuration '{{value}}' created", "REPORT_CONFIGURATION_CREATION_FAILED": "Failed to create report configuration '{{value}}'", "REPORT_CONFIGURATION_UPDATED": "Report configuration '{{value}}' updated", "REPORT_CONFIGURATION_UPDATE_FAILED": "Failed to update report configuration '{{value}}'"}, "USERS": {"EMAIL": "Email", "FIRST_NAME": "First Name", "LAST_NAME": "Last Name", "NEW_USER": "New User", "USER": "User", "ROLES": "Roles", "USER_CREATED": "User '{{value}}' created", "USER_CREATION_FAILED": "Failed to create user '{{value}}'", "USER_UPDATED": "User '{{value}}' updated", "USER_UPDATE_FAILED": "Failed to update user '{{value}}'"}, "ROLES": {"NAME": "Name", "NEW_ROLE": "New Role", "ROLE": "Role", "RIGHTS": "Rights", "ROLE_CREATED": "Role '{{value}}' created", "ROLE_CREATION_FAILED": "Failed to create role '{{value}}'", "ROLE_UPDATED": "Role '{{value}}' updated", "ROLE_UPDATE_FAILED": "Failed to update role '{{value}}'"}, "WORKFLOW": {"TYPE": "Workflow Type", "ORDER": "Order Workflow", "SUBSCRIPTION": "Subscription Workflow", "NO_DIAGRAM_TITLE": "Workflow Definition Available", "NO_DIAGRAM_MESSAGE": "The workflow definition exists and is functional, but visual diagram information is not available for display. The workflow can still be executed normally."}, "FAILED_JOBS": {"BUSINESS_KEY": "Business Key", "CREATED_AT": "Created At", "EXCEPTION_MESSAGE": "Exception Message", "REMAINING_RETRIES": "Remaining Retries", "RETRY": "Retry"}, "HISTORIC_ACTIVITIES": {"ACTIVITY_TYPE": "Activity Type", "STARTED_AT": "Started At", "ENDED_AT": "Ended At", "BUSINESS_KEY": "Business Key", "ACTIVITY_ID": "Activity ID", "ACTIVITY_NAME": "Activity Name", "DELETE_REASON": "Delete Reason"}, "ENUM": {"PaymentAssignmentEntityTargetType": {"ORDER": "Order", "DOCUMENT": "Document"}, "DebitCreditIndicator": {"DEBIT": "Debit", "CREDIT": "Credit"}, "NotificationType": {"DOCUMENT_CREATED": "Document Created", "POSTING_RECORD_CREATED": "Posting Record Created", "PAYMENT_ASSIGNED": "Payment Assigned", "CUSTOMER_CREATED": "Customer Created", "PAYMENT_TRANSACTION_CREATED": "Payment Transaction Created", "SUPPLIER_CREATED": "Supplier Created"}, "TargetType": {"ORDER": "Order", "DOCUMENT": "Document", "PAYMENT_TRANSACTION": "Payment Transaction", "CUSTOMER": "Customer", "POSTING_RECORD": "Posting Record", "PAYMENT_ASSIGNMENT": "Payment Assignment"}, "PaymentAssignmentTargetType": {"ORDER": "Order", "FINAL": "Final", "REVERSED": "Reversed", "DEPOSIT": "<PERSON><PERSON><PERSON><PERSON>", "SELFBILLING": "Selfbilling", "SELFBILLING_REVERSED": "Selfbilling Reversed", "COMMISSION": "Commission", "COMMISSION_REVERSED": "Commission Reversed"}, "SendingStatus": {"OPEN": "Open", "SENT": "<PERSON><PERSON>", "ERROR": "Error", "DISMISSED": "Dismissed"}, "ApprovalType": {"ORDER_CANCELLATION": "Order Cancellation", "PAYMENT_ASSIGNMENT": "Payment Assignment"}, "ApprovalStatus": {"PENDING": "Pending", "APPROVED": "Approved", "REJECTED": "Rejected"}, "DocumentType": {"FINAL": "Final", "DEPOSIT": "<PERSON><PERSON><PERSON><PERSON>", "REVERSED": "Reversed", "SELFBILLING": "Selfbilling", "SELFBILLING_REVERSED": "Selfbilling Reversed", "COMMISSION": "Commission", "COMMISSION_REVERSED": "Commission Reversed"}, "PaymentStatus": {"UNPAID": "Unpaid", "PARTIALLY_PAID": "Partially Paid", "FULLY_PAID": "<PERSON>y Paid"}, "BalanceCaseItemType": {"PAY_IN": "Pay In", "PAY_IN_REVERSED": "Pay In Reversed", "PAY_OUT": "Pay Out", "PAY_OUT_REVERSED": "Pay Out Reversed", "DEPOSIT_INVOICE": "Deposit Invoice", "DEPOSIT_CREDIT_NOTE": "Deposit Credit Note", "INVOICE": "Invoice", "CREDIT_NOTE": "Credit Note"}, "BalanceCaseStatus": {"UNDERBALANCED": "Underbalanced", "BALANCED": "Balanced", "OVERBALANCED": "Overbalanced"}, "PostingRecordTargetType": {"DOCUMENT": "Document", "PAYMENT_TRANSACTION": "Payment Transaction", "PAYMENT_ASSIGNMENT": "Payment Assignment"}, "OrderStatus": {"INIT": "Initial", "CANCELLED": "Cancelled", "BILLED": "Billed", "REVERSED": "Reversed", "PARTIALLY_BILLED": "Partially Billed", "WAITING_FOR_APPROVAL": "Waiting for <PERSON><PERSON><PERSON><PERSON>"}, "ReportType": {"SUBLEDGER": "Subledger", "PAYMENT": "Payments", "APPROVAL": "Approvals"}, "ReportInterval": {"DAILY": "Daily", "WEEKLY": "Weekly", "MONTHLY": "Monthly", "QUARTERLY": "Quarterly", "YEARLY": "Yearly"}, "ReportFormat": {"JSON": "JSON", "CSV": "CSV"}, "BookingAccountType": {"ASSET": "<PERSON><PERSON>", "LIABILITY": "Liability", "EQUITY": "Equity", "REVENUE": "Revenue", "EXPENSE": "Expense", "INCOME": "Income"}, "BookingRuleType": {"TAX_CODE": "Tax Code", "DOCUMENT_ITEM": "Document Item", "PAYMENT_TRANSACTION": "Payment Transaction", "PAYMENT_ASSIGNMENT": "Payment Assignment"}, "PaymentAccountType": {"BANK_ACCOUNT": "Bank Account", "PAYPAL": "<PERSON><PERSON>"}, "PaymentAssignmentType": {"ASSIGNED": "Assigned", "REVERSED": "Reversed"}, "PaymentAssignmentStatus": {"ACTIVE": "Active", "REVERSED": "Reversed"}, "PaymentAssignmentOrigin": {"MANUAL": "Manual", "AUTOMATED": "Automated"}, "PaymentCategory": {"BANK_FEES": "Bank Fees", "CASH_POOLING": "Cash Pooling", "CUSTOMER_PAYMENT": "Customer Payment", "PAYOUT": "Payout", "NOT_CATEGORIZED": "Not Categorized"}, "PaymentTransactionAssignmentStatus": {"UNASSIGNED": "Unassigned", "PARTIALLY_ASSIGNED": "Partially Assigned", "COMPLETELY_ASSIGNED": "Completely Assigned"}, "FinancingType": {"CASH": "Cash", "FINANCED": "Financed", "LEASING": "Leasing"}, "UserRight": {"CUSTOMERS_READ": "Customers Read", "CUSTOMERS_WRITE": "Customers Write", "SUPPLIERS_READ": "Suppliers Read", "SUPPLIERS_WRITE": "Suppliers Write", "ORDER_CAPTURE": "Order Capture", "ORDERS_READ": "Orders Read", "ORDERS_WRITE": "Orders Write", "PREDEFINED_ITEMS_READ": "Predefined Items Read", "PREDEFINED_ITEMS_WRITE": "Predefined Items Write", "OPEN_ITEMS_READ": "Open Items Read", "DOCUMENTS_READ": "Documents Read", "TRANSLATIONS_READ": "Translations Read", "TRANSLATIONS_WRITE": "Translations Write", "REPORTS_READ": "Reports Read", "BUSINESS_SEGMENTS_READ": "Business Segments Read", "BUSINESS_SEGMENTS_WRITE": "Business Segments Write", "APPROVALS_READ": "Approvals Read", "APPROVALS_WRITE": "Approvals Write", "PAYMENT_ACCOUNTS_READ": "Payment Accounts Read", "PAYMENT_ACCOUNTS_WRITE": "Payment Accounts Write", "SETTLEMENT_REPORTS_READ": "Settlement Reports Read", "SETTLEMENT_REPORTS_WRITE": "Settlement Reports Write", "PAYMENT_CATEGORIZATION_RULES_READ": "Payment Categorization Rules Read", "PAYMENT_CATEGORIZATION_RULES_WRITE": "Payment Categorization Rules Write", "PAYMENT_TRANSACTIONS_READ": "Payment Transactions Read", "PAYMENT_ASSIGNMENTS_READ": "Payment Assignments Read", "PAYMENT_ASSIGNMENTS_WRITE": "Payment Assignments Write", "BOOKING_ACCOUNTS_READ": "Booking Accounts Read", "BOOKING_ACCOUNTS_WRITE": "Booking Accounts Write", "BOOKING_RULES_READ": "Booking Rules Read", "BOOKING_RULES_WRITE": "Booking Rules Write", "POSTING_RECORDS_READ": "Posting Records Read", "POSTING_RECORDS_WRITE": "Posting Records Write", "CURRENCY_EXCHANGE_RATE_READ": "Currency Exchange Rate Read", "MANUAL_BOOKING": "Manual Booking", "SENT_ARCHIVE_READ": "Sent Archive Read", "SENT_ARCHIVE_WRITE": "Sent Archive Write", "NOTIFICATIONS_READ": "Notifications Read", "NOTIFICATIONS_WRITE": "Notifications Write", "NOTIFICATION_RECEIVERS_READ": "Notification Receivers Read", "NOTIFICATION_RECEIVERS_WRITE": "Notification Receivers Write", "REPORT_CONFIGURATIONS_READ": "Report Configurations Read", "REPORT_CONFIGURATIONS_WRITE": "Report Configurations Write", "USERS_READ": "Users Read", "USERS_WRITE": "Users Write", "ROLES_READ": "Roles Read", "ROLES_WRITE": "Roles Write", "WORKFLOW_READ": "Workflow Read", "INTERNAL_JOBS_READ": "Internal Jobs Read", "INTERNAL_JOBS_WRITE": "Internal Jobs Write"}, "OriginEnum": {"API": "API", "WEB_PORTAL": "Web Portal"}, "UserLanguage": {"TENANT_DEFAULT": "Tenant <PERSON>", "ENGLISH": "English", "GERMAN": "German"}, "ProcessingStatus": {"INIT": "Initial", "ERROR": "Error", "PROCESSED": "Processed", "ORDER_CREATED": "Order Created", "CREDIT_NOTE_CREATED": "Credit Note Created", "CREDIT_NOTE_POSTING_RECORD_CREATED": "Credit Note Posting Record Created", "CREDIT_NOTE_PAYOUT_CREATED": "Credit Note Payout Created", "CREDIT_NOTE_PAYOUT_POSTING_RECORD_CREATED": "Credit Note Payout Posting Record Created", "DEPOSIT_INVOICE_CREATED": "Deposit Invoice Created", "DEPOSIT_PAYMENT_POSTING_RECORD_CREATED": "Deposit Payment Posting Record Created", "DEPOSIT_POSTING_RECORD_CREATED": "Deposit Posting Record Created", "DEPOSIT_PAYMENT_DEBITED": "Deposit Payment Debited", "FINAL_INVOICE_CREATED": "Final Invoice Created", "FINAL_INVOICE_CREDIT_ONLY_CREATED": "Final Invoice Credit Only Created", "FINAL_INVOICE_DEBIT_ONLY_CREATED": "Final Invoice Debit Only Created", "FINAL_INVOICE_PAYMENT_POSTING_RECORD_CREATED": "Final Invoice Payment Posting Record Created", "FINAL_INVOICE_POSTING_RECORD_CREATED": "Final Invoice Posting Record Created", "FINAL_INVOICE_PAYMENT_DEBITED": "Final Invoice Payment Debited", "PAYMENT_ASSIGNMENT_CREATED": "Payment Assignment Created", "PAYMENT_ASSIGNMENT_POSTING_RECORD_CREATED": "Payment Assignment Posting Record Created", "CANCELLATION_APPROVAL_CREATED": "Cancellation Approved Created", "CANCELLATION_APPROVED": "Cancellation Approved", "CANCELLATION_REJECTED": "Cancellation Rejected", "CUSTOMER_EMAIL_SENT": "Customer <PERSON><PERSON>", "FINAL_SELFBILLING_INVOICE_POSTING_RECORD_CREATED": "Final Selfbilling Invoice Posting Record Created", "FINAL_SELFBILLING_INVOICE_CREATED": "Final Selfbilling Invoice Created", "FINAL_SELFBILLING_INVOICE_REVERSED_POSTING_RECORD_CREATED": "Final Selfbilling Invoice Reversed Posting Record Created", "FINAL_SELFBILLING_INVOICE_REVERSED_CREATED": "Final Selfbilling Invoice Reversed Created", "FINAL_COMMISSION_INVOICE_CREATED": "Final Commission Invoice Created", "FINAL_COMMISSION_INVOICE_POSTING_RECORD_CREATED": "Final Commission Invoice Posting Record Created", "FINAL_COMMISSION_INVOICE_REVERSED_CREATED": "Final Commission Invoice Reversed Created", "FINAL_COMMISSION_INVOICE_REVERSED_POSTING_RECORD_CREATED": "Final Commission Invoice Reversed Posting Record Created", "ORDER_UPDATED": "Order Updated"}, "AuthorizationType": {"NONE": "None", "BASIC": "Basic", "FIXED_API_TOKEN": "Fixed API Token", "OAUTH2_WITH_CLIENT_ID_AND_SECRET": "OAuth2 with Client ID and Secret"}, "SentArchiveType": {"EMAIL": "Email", "REPORT": "Report"}, "Language": {"AF": "Afrikaans", "AM": "Amharic", "AR": "Arabic", "AZ": "Azerbaijani", "BE": "Belarusian", "BG": "Bulgarian", "BN": "Bengali", "BS": "Bosnian", "CA": "Catalan", "CEB": "Cebuano", "CO": "Corsican", "CS": "Czech", "CY": "Welsh", "DA": "Danish", "DE": "German", "EL": "Greek", "EN": "English", "EO": "Esperanto", "ES": "Spanish", "ET": "Estonian", "EU": "Basque", "FA": "Persian", "FI": "Finnish", "FR": "French", "FY": "Frisian", "GA": "Irish", "GD": "Scots Gaelic", "GL": "Galician", "GU": "Gujarati", "HA": "Hausa", "HAW": "Hawaiian", "HE": "Hebrew", "HI": "Hindi", "HMN": "Hmong", "HR": "Croatian", "HT": "Haitian Creole", "HU": "Hungarian", "HY": "Armenian", "ID": "Indonesian", "IG": "Igbo", "IS": "Icelandic", "IT": "Italian", "JA": "Japanese", "JV": "Javanese", "KA": "Georgian", "KK": "Kazakh", "KM": "Khmer", "KN": "Kannada", "KO": "Korean", "KU": "Kurdish", "KY": "Kyrgyz", "LA": "Latin", "LB": "Luxembourgish", "LO": "Lao", "LT": "Lithuanian", "LV": "Latvian", "MG": "Malagasy", "MI": "<PERSON><PERSON>", "MK": "Macedonian", "ML": "Malayalam", "MN": "Mongolian", "MR": "Marathi", "MS": "Malay", "MT": "Maltese", "MY": "Burmese", "NE": "Nepali", "NL": "Dutch", "NO": "Norwegian", "NY": "Chichewa", "PA": "Punjabi", "PL": "Polish", "PS": "Pashto", "PT": "Portuguese", "RO": "Romanian", "RU": "Russian", "SD": "Sindhi", "SI": "Sinhala", "SK": "Slovak", "SL": "Slovenian", "SM": "Samoan", "SN": "<PERSON><PERSON><PERSON>", "SO": "Somali", "SQ": "Albanian", "SR": "Serbian", "ST": "<PERSON><PERSON><PERSON><PERSON>", "SU": "Sundanese", "SV": "Swedish", "SW": "Swahili", "TA": "Tamil", "TE": "Telugu", "TG": "Tajik", "TH": "Thai", "TL": "Tagalog", "TR": "Turkish", "UK": "Ukrainian", "UR": "Urdu", "UZ": "Uzbek", "VI": "Vietnamese", "XH": "Xhosa", "YI": "Yiddish", "YO": "Yoruba", "ZH_CN": "Chinese (Simplified)", "ZH_TW": "Chinese (Traditional)", "ZU": "Zulu"}, "TranslationType": {"EMAIL": "Email", "DOCUMENT": "Document"}}, "SUBSCRIPTIONS": {"TITLE": "Subscriptions", "CREATE_NEW": "Create New Subscription", "EDIT": "Edit Subscription", "DETAILS": "Subscription Details", "KEY": "Subscription Key", "NAME": "Name", "DESCRIPTION": "Description", "CUSTOMER": "Customer", "STATUS": "Status", "FREQUENCY": "Billing Frequency", "AMOUNT": "Amount", "CURRENCY": "<PERSON><PERSON><PERSON><PERSON>", "START_DATE": "Start Date", "END_DATE": "End Date", "NEXT_BILLING_DATE": "Next Billing Date", "CREATED_SUCCESS": "Subscription created successfully", "UPDATED_SUCCESS": "Subscription updated successfully", "DELETED_SUCCESS": "Subscription deleted successfully", "ERROR_LOADING": "Error loading subscription", "ERROR_LOADING_CUSTOMERS": "Error loading customers", "ERROR_CREATING": "Error creating subscription", "ERROR_UPDATING": "Error updating subscription", "ERROR_DELETING": "Error deleting subscription", "CONFIRM_DELETE": "Are you sure you want to delete this subscription?"}, "SUBSCRIPTION_STATUS": {"ACTIVE": "Active", "PAUSED": "Paused", "CANCELLED": "Cancelled", "EXPIRED": "Expired"}, "SUBSCRIPTION_FREQUENCY": {"DAILY": "Daily", "WEEKLY": "Weekly", "MONTHLY": "Monthly", "QUARTERLY": "Quarterly", "YEARLY": "Yearly"}}