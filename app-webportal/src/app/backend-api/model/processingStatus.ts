/**
 * billing-solution frontend
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


/**
 * The processing status of the order or subscription
 */
export type ProcessingStatus = 'INIT' | 'ERROR' | 'PROCESSED' | 'ORDER_CREATED' | 'CREDIT_NOTE_CREATED' | 'CREDIT_NOTE_POSTING_RECORD_CREATED' | 'CREDIT_NOTE_PAYOUT_CREATED' | 'CREDIT_NOTE_PAYOUT_POSTING_RECORD_CREATED' | 'DEPOSIT_INVOICE_CREATED' | 'DEPOSIT_PAYMENT_POSTING_RECORD_CREATED' | 'DEPOSIT_POSTING_RECORD_CREATED' | 'DEPOSIT_PAYMENT_DEBITED' | 'FINAL_INVOICE_CREATED' | 'FINAL_INVOICE_CREDIT_ONLY_CREATED' | 'FINAL_INVOICE_DEBIT_ONLY_CREATED' | 'FINAL_INVOICE_PAYMENT_POSTING_RECORD_CREATED' | 'FINAL_INVOICE_POSTING_RECORD_CREATED' | 'FINAL_INVOICE_PAYMENT_DEBITED' | 'FINAL_SELFBILLING_INVOICE_CREATED' | 'FINAL_SELFBILLING_INVOICE_POSTING_RECORD_CREATED' | 'FINAL_SELFBILLING_INVOICE_REVERSED_CREATED' | 'FINAL_SELFBILLING_INVOICE_REVERSED_POSTING_RECORD_CREATED' | 'FINAL_COMMISSION_INVOICE_CREATED' | 'FINAL_COMMISSION_INVOICE_POSTING_RECORD_CREATED' | 'FINAL_COMMISSION_INVOICE_REVERSED_CREATED' | 'FINAL_COMMISSION_INVOICE_REVERSED_POSTING_RECORD_CREATED' | 'ORDER_UPDATED' | 'PAYMENT_ASSIGNMENT_CREATED' | 'PAYMENT_ASSIGNMENT_POSTING_RECORD_CREATED' | 'CANCELLATION_APPROVAL_CREATED' | 'CANCELLATION_APPROVED' | 'CANCELLATION_REJECTED' | 'CUSTOMER_EMAIL_SENT' | 'SUBSCRIPTION_CREATED' | 'SUBSCRIPTION_ACTIVATED' | 'SUBSCRIPTION_BILLING_PROCESSED' | 'SUBSCRIPTION_INVOICE_CREATED' | 'SUBSCRIPTION_INVOICE_SENT' | 'SUBSCRIPTION_PAUSED' | 'SUBSCRIPTION_RESUMED' | 'SUBSCRIPTION_CANCELLED' | 'SUBSCRIPTION_EXPIRED' | 'SUBSCRIPTION_UPDATED';

export const ProcessingStatus = {
    Init: 'INIT' as ProcessingStatus,
    Error: 'ERROR' as ProcessingStatus,
    Processed: 'PROCESSED' as ProcessingStatus,
    OrderCreated: 'ORDER_CREATED' as ProcessingStatus,
    CreditNoteCreated: 'CREDIT_NOTE_CREATED' as ProcessingStatus,
    CreditNotePostingRecordCreated: 'CREDIT_NOTE_POSTING_RECORD_CREATED' as ProcessingStatus,
    CreditNotePayoutCreated: 'CREDIT_NOTE_PAYOUT_CREATED' as ProcessingStatus,
    CreditNotePayoutPostingRecordCreated: 'CREDIT_NOTE_PAYOUT_POSTING_RECORD_CREATED' as ProcessingStatus,
    DepositInvoiceCreated: 'DEPOSIT_INVOICE_CREATED' as ProcessingStatus,
    DepositPaymentPostingRecordCreated: 'DEPOSIT_PAYMENT_POSTING_RECORD_CREATED' as ProcessingStatus,
    DepositPostingRecordCreated: 'DEPOSIT_POSTING_RECORD_CREATED' as ProcessingStatus,
    DepositPaymentDebited: 'DEPOSIT_PAYMENT_DEBITED' as ProcessingStatus,
    FinalInvoiceCreated: 'FINAL_INVOICE_CREATED' as ProcessingStatus,
    FinalInvoiceCreditOnlyCreated: 'FINAL_INVOICE_CREDIT_ONLY_CREATED' as ProcessingStatus,
    FinalInvoiceDebitOnlyCreated: 'FINAL_INVOICE_DEBIT_ONLY_CREATED' as ProcessingStatus,
    FinalInvoicePaymentPostingRecordCreated: 'FINAL_INVOICE_PAYMENT_POSTING_RECORD_CREATED' as ProcessingStatus,
    FinalInvoicePostingRecordCreated: 'FINAL_INVOICE_POSTING_RECORD_CREATED' as ProcessingStatus,
    FinalInvoicePaymentDebited: 'FINAL_INVOICE_PAYMENT_DEBITED' as ProcessingStatus,
    FinalSelfbillingInvoiceCreated: 'FINAL_SELFBILLING_INVOICE_CREATED' as ProcessingStatus,
    FinalSelfbillingInvoicePostingRecordCreated: 'FINAL_SELFBILLING_INVOICE_POSTING_RECORD_CREATED' as ProcessingStatus,
    FinalSelfbillingInvoiceReversedCreated: 'FINAL_SELFBILLING_INVOICE_REVERSED_CREATED' as ProcessingStatus,
    FinalSelfbillingInvoiceReversedPostingRecordCreated: 'FINAL_SELFBILLING_INVOICE_REVERSED_POSTING_RECORD_CREATED' as ProcessingStatus,
    FinalCommissionInvoiceCreated: 'FINAL_COMMISSION_INVOICE_CREATED' as ProcessingStatus,
    FinalCommissionInvoicePostingRecordCreated: 'FINAL_COMMISSION_INVOICE_POSTING_RECORD_CREATED' as ProcessingStatus,
    FinalCommissionInvoiceReversedCreated: 'FINAL_COMMISSION_INVOICE_REVERSED_CREATED' as ProcessingStatus,
    FinalCommissionInvoiceReversedPostingRecordCreated: 'FINAL_COMMISSION_INVOICE_REVERSED_POSTING_RECORD_CREATED' as ProcessingStatus,
    OrderUpdated: 'ORDER_UPDATED' as ProcessingStatus,
    PaymentAssignmentCreated: 'PAYMENT_ASSIGNMENT_CREATED' as ProcessingStatus,
    PaymentAssignmentPostingRecordCreated: 'PAYMENT_ASSIGNMENT_POSTING_RECORD_CREATED' as ProcessingStatus,
    CancellationApprovalCreated: 'CANCELLATION_APPROVAL_CREATED' as ProcessingStatus,
    CancellationApproved: 'CANCELLATION_APPROVED' as ProcessingStatus,
    CancellationRejected: 'CANCELLATION_REJECTED' as ProcessingStatus,
    CustomerEmailSent: 'CUSTOMER_EMAIL_SENT' as ProcessingStatus,
    SubscriptionCreated: 'SUBSCRIPTION_CREATED' as ProcessingStatus,
    SubscriptionActivated: 'SUBSCRIPTION_ACTIVATED' as ProcessingStatus,
    SubscriptionBillingProcessed: 'SUBSCRIPTION_BILLING_PROCESSED' as ProcessingStatus,
    SubscriptionInvoiceCreated: 'SUBSCRIPTION_INVOICE_CREATED' as ProcessingStatus,
    SubscriptionInvoiceSent: 'SUBSCRIPTION_INVOICE_SENT' as ProcessingStatus,
    SubscriptionPaused: 'SUBSCRIPTION_PAUSED' as ProcessingStatus,
    SubscriptionResumed: 'SUBSCRIPTION_RESUMED' as ProcessingStatus,
    SubscriptionCancelled: 'SUBSCRIPTION_CANCELLED' as ProcessingStatus,
    SubscriptionExpired: 'SUBSCRIPTION_EXPIRED' as ProcessingStatus,
    SubscriptionUpdated: 'SUBSCRIPTION_UPDATED' as ProcessingStatus
};

