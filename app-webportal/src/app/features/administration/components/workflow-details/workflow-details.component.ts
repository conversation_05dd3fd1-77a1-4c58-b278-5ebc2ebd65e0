import { Component, OnInit } from '@angular/core'
import { WebPortalWorkflowService, WorkflowType } from 'src/app/backend-api'
import { SessionService } from '../../../../services/session.service'
import { Subject, takeUntil } from 'rxjs'
import NavigatedViewer from 'bpmn-js/lib/NavigatedViewer'
import ZoomScroll from 'diagram-js/lib/navigation/zoomscroll/ZoomScroll'
import Canvas from 'diagram-js/lib/core/Canvas'
import { RootLike } from 'diagram-js/lib/model/Types'

@Component({
  standalone: false,
  selector: 'app-workflow-details',
  templateUrl: './workflow-details.component.html',
  styleUrls: ['./workflow-details.component.css'],
})
export class WorkflowDetailsComponent implements OnInit {
  private viewer: NavigatedViewer
  private rootElement: RootLike
  private destroy$ = new Subject<void>()
  isLoading: boolean
  hasDiagramError: boolean = false
  selectedWorkflowType: WorkflowType = WorkflowType.Order
  workflowTypes = [
    { value: WorkflowType.Order, label: 'WORKFLOW.ORDER' },
    { value: WorkflowType.Subscription, label: 'WORKFLOW.SUBSCRIPTION' }
  ]

  constructor(
    private webPortalWorkflowService: WebPortalWorkflowService,
    private sessionService: SessionService
  ) {}

  ngOnInit(): void {
    this.loadWorkflowDefinition()
  }

  private loadWorkflowDefinition(): void {
    this.viewer = new NavigatedViewer({ container: '#workflow-canvas' })
    this.isLoading = true
    this.hasDiagramError = false
    this.webPortalWorkflowService
      .loadWorkflowDefinition(this.sessionService.getTenantKey(), this.selectedWorkflowType, 'body', false, {
        httpHeaderAccept: 'application/xml',
      })
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (data: any) => {
          try {
            this.blobToString(data as Blob).then((xml: string) => {
              this.viewer
                .importXML(xml)
                .then((r) => {
                  const canvas = this.viewer.get('canvas') as Canvas
                  canvas.zoom('fit-viewport')
                  this.rootElement = canvas.getRootElement()
                  console.log('Imported XML')
                })
                .catch((e) => {
                  console.error('Error importing XML: ' + e)
                  if (e.toString().includes('no diagram to display')) {
                    this.hasDiagramError = true
                    console.warn(`The ${this.selectedWorkflowType} workflow definition exists but lacks visual diagram information. The workflow is functional but cannot be displayed.`)
                  }
                })
            })
          } catch (err) {
            console.log('Workflow definition could not be loaded: ' + err)
          }
          this.isLoading = false
        },
        error: (error: any) => {
          console.error('Error loading workflow definition', error)
          this.isLoading = false
        },
      })
  }

  private blobToString(blob: Blob): Promise<string> {
    return new Promise((resolve, reject) => {
      const reader = new FileReader()
      reader.onloadend = () => resolve(reader.result as string)
      reader.onerror = reject
      reader.readAsText(blob)
    })
  }

  zoomIn() {
    const zoomScroll = this.viewer.get('zoomScroll') as ZoomScroll
    zoomScroll.stepZoom(1)
  }

  zoomOut() {
    const zoomScroll = this.viewer.get('zoomScroll') as ZoomScroll
    zoomScroll.stepZoom(-1)
  }

  back() {
    const canvas = this.viewer.get('canvas') as Canvas
    canvas.setRootElement(this.rootElement)
    canvas.zoom('fit-viewport')
  }

  onWorkflowTypeChange(): void {
    if (this.viewer) {
      this.viewer.destroy()
    }
    this.loadWorkflowDefinition()
  }
}
