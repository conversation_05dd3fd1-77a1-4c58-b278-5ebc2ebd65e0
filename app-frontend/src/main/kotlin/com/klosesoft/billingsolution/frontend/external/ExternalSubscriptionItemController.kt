package com.klosesoft.billingsolution.frontend.external

import com.klosesoft.billingsolution.domain.logic.api.service.WorkflowApiService
import com.klosesoft.billingsolution.domain.logic.api.service.load.SubscriptionItemLoadDomainApiService
import com.klosesoft.billingsolution.domain.model.valueobject.UpdateSubscriptionAction
import com.klosesoft.billingsolution.frontend.ControllerUtil.createValidPageable
import com.klosesoft.billingsolution.frontend.auth.TenantUtil
import com.klosesoft.billingsolution.frontend.external.mapper.ExternalSubscriptionMapper
import com.klosesoft.billingsolution.generated.api.external.server.model.ExternalSubscriptionItemRequestDto
import com.klosesoft.billingsolution.generated.api.external.server.model.ExternalSubscriptionItemResponseDto
import com.klosesoft.billingsolution.generated.api.external.server.model.ExternalSubscriptionItemsPagedResponseDto
import org.springframework.http.ResponseEntity
import org.springframework.transaction.annotation.Transactional
import org.springframework.web.bind.annotation.RestController

@RestController
class ExternalSubscriptionItemController(
    private val subscriptionItemLoadService: SubscriptionItemLoadDomainApiService,
    private val subscriptionMapper: ExternalSubscriptionMapper,
    private val workflowApiService: WorkflowApiService,
) {
    @Transactional(readOnly = true)
    suspend fun loadSubscriptionItems(
        subscriptionKey: String,
        page: Int,
        size: Int,
        sort: String?,
        direction: String?,
        filter: String?,
    ): ResponseEntity<ExternalSubscriptionItemsPagedResponseDto> {
        val subscriptionItemsPage = subscriptionItemLoadService.findSubscriptionItems(
            tenantKey = TenantUtil.getTenantKey(),
            subscriptionKey = subscriptionKey,
            rsqlFilter = filter,
            pageable = createValidPageable(page, size, sort, direction),
        )

        return ResponseEntity.ok(
            subscriptionMapper.toExternalSubscriptionItemsPagedResponse(subscriptionItemsPage),
        )
    }

    @Transactional(readOnly = true)
    suspend fun loadSubscriptionItem(
        subscriptionKey: String,
        subscriptionItemKey: String,
    ): ResponseEntity<ExternalSubscriptionItemResponseDto> {
        val subscriptionItem = subscriptionItemLoadService.findSubscriptionItem(
            TenantUtil.getTenantKey(),
            subscriptionKey,
            subscriptionItemKey,
        )
        return ResponseEntity.ok(
            subscriptionMapper.toExternal(subscriptionItem),
        )
    }

    @Transactional
    suspend fun addSubscriptionItem(
        subscriptionKey: String,
        externalSubscriptionItemRequestDto: ExternalSubscriptionItemRequestDto,
    ): ResponseEntity<Unit> {
        val subscriptionItemDomainDto = subscriptionMapper.toDomainDto(externalSubscriptionItemRequestDto)

        workflowApiService.sendUpdateSubscriptionEvent(
            tenantKey = TenantUtil.getTenantKey(),
            subscriptionKey = subscriptionKey,
            action = UpdateSubscriptionAction.ADD_SUBSCRIPTION_ITEM,
            subscriptionItemRequestDomainDto = subscriptionItemDomainDto,
        )

        return ResponseEntity.accepted().build()
    }

    @Transactional
    suspend fun updateSubscriptionItem(
        subscriptionKey: String,
        subscriptionItemKey: String,
        externalSubscriptionItemRequestDto: ExternalSubscriptionItemRequestDto,
    ): ResponseEntity<Unit> {
        val subscriptionItemDomainDto = subscriptionMapper.toDomainDto(externalSubscriptionItemRequestDto)

        workflowApiService.sendUpdateSubscriptionEvent(
            tenantKey = TenantUtil.getTenantKey(),
            subscriptionKey = subscriptionKey,
            action = UpdateSubscriptionAction.UPDATE_SUBSCRIPTION_ITEM,
            subscriptionItemRequestDomainDto = subscriptionItemDomainDto,
        )

        return ResponseEntity.accepted().build()
    }

    @Transactional
    suspend fun deleteSubscriptionItem(
        subscriptionKey: String,
        subscriptionItemKey: String,
    ): ResponseEntity<Unit> {
        workflowApiService.sendUpdateSubscriptionEvent(
            tenantKey = TenantUtil.getTenantKey(),
            subscriptionKey = subscriptionKey,
            action = UpdateSubscriptionAction.DELETE_SUBSCRIPTION_ITEM,
            subscriptionItemKey = subscriptionItemKey,
        )

        return ResponseEntity.accepted().build()
    }
}
