package com.klosesoft.billingsolution.frontend.webportal

import com.klosesoft.billingsolution.domain.logic.api.service.direct.SubscriptionDomainApiService
import com.klosesoft.billingsolution.domain.logic.api.service.load.SubscriptionLoadDomainApiService
import com.klosesoft.billingsolution.domain.logic.api.service.load.UserLoadDomainApiService
import com.klosesoft.billingsolution.domain.model.valueobject.Right
import com.klosesoft.billingsolution.frontend.ControllerUtil.createValidPageable
import com.klosesoft.billingsolution.frontend.webportal.auth.WebPortalTenantAccessAllowedChecker
import com.klosesoft.billingsolution.frontend.webportal.mapper.WebPortalSubscriptionMapper
import com.klosesoft.billingsolution.generated.api.webportal.server.WebPortalSubscriptionApi
import com.klosesoft.billingsolution.generated.api.webportal.server.model.WebPortalSubscriptionDetailsDataDto
import com.klosesoft.billingsolution.generated.api.webportal.server.model.WebPortalSubscriptionDetailsResponseDto
import com.klosesoft.billingsolution.generated.api.webportal.server.model.WebPortalSubscriptionsPagedResponseDto
import org.springframework.http.ResponseEntity
import org.springframework.transaction.annotation.Transactional
import org.springframework.web.bind.annotation.RestController

@RestController
@WebPortalApiAuth
class WebPortalSubscriptionController(
    private val subscriptionLoadDomainApiService: SubscriptionLoadDomainApiService,
    private val subscriptionDomainApiService: SubscriptionDomainApiService,
    private val webPortalTenantAccessAllowedChecker: WebPortalTenantAccessAllowedChecker,
    private val webPortalSubscriptionMapper: WebPortalSubscriptionMapper,
    private val userLoadDomainApiService: UserLoadDomainApiService,
) : WebPortalSubscriptionApi {

    @Transactional(readOnly = true)
    override suspend fun loadSubscriptions(
        xTenantKey: String,
        page: Int?,
        size: Int?,
        sort: String?,
        direction: String?,
        filter: String?,
    ): ResponseEntity<WebPortalSubscriptionsPagedResponseDto> {
        webPortalTenantAccessAllowedChecker.throwExceptionIfNotAuthorizedForCurrentUser(xTenantKey, Right.CUSTOMERS_READ)

        val subscriptionsPage = subscriptionLoadDomainApiService.findAllSubscriptions(
            tenantKey = xTenantKey,
            rsqlFilter = filter,
            pageable = createValidPageable(page, size, sort, direction),
        )

        return ResponseEntity.ok(webPortalSubscriptionMapper.toWebPortalPagedSubscriptionsPageResponse(subscriptionsPage))
    }

    @Transactional
    override suspend fun createSubscription(
        xTenantKey: String,
        webPortalSubscriptionDetailsDataDto: WebPortalSubscriptionDetailsDataDto,
    ): ResponseEntity<WebPortalSubscriptionDetailsResponseDto> {
        webPortalTenantAccessAllowedChecker.throwExceptionIfNotAuthorizedForCurrentUser(xTenantKey, Right.CUSTOMERS_WRITE)

        val subscriptionDomainDto = webPortalSubscriptionMapper.toDomainDto(webPortalSubscriptionDetailsDataDto)

        val user = userLoadDomainApiService.findByCognitoId(WebPortalControllerUtil.getCognitoId())

        val createdSubscription = subscriptionDomainApiService.createSubscription(
            tenantKey = xTenantKey,
            userKey = user.key,
            subscriptionDomainDto = subscriptionDomainDto,
        )

        return ResponseEntity.status(201).body(
            webPortalSubscriptionMapper.toWebPortalSubscriptionDetailsResponse(createdSubscription)
        )
    }

    @Transactional(readOnly = true)
    override suspend fun loadSubscription(
        xTenantKey: String,
        subscriptionKey: String,
    ): ResponseEntity<WebPortalSubscriptionDetailsResponseDto> {
        webPortalTenantAccessAllowedChecker.throwExceptionIfNotAuthorizedForCurrentUser(xTenantKey, Right.CUSTOMERS_READ)

        val subscription = subscriptionLoadDomainApiService.findSubscription(xTenantKey, subscriptionKey)

        return ResponseEntity.ok(webPortalSubscriptionMapper.toWebPortalSubscriptionDetailsResponse(subscription))
    }

    @Transactional
    override suspend fun updateSubscription(
        xTenantKey: String,
        subscriptionKey: String,
        webPortalSubscriptionDetailsDataDto: WebPortalSubscriptionDetailsDataDto,
    ): ResponseEntity<WebPortalSubscriptionDetailsResponseDto> {
        webPortalTenantAccessAllowedChecker.throwExceptionIfNotAuthorizedForCurrentUser(xTenantKey, Right.CUSTOMERS_WRITE)

        val subscriptionDomainDto = webPortalSubscriptionMapper.toDomainDto(webPortalSubscriptionDetailsDataDto)

        val user = userLoadDomainApiService.findByCognitoId(WebPortalControllerUtil.getCognitoId())

        val updatedSubscription = subscriptionDomainApiService.updateSubscriptionData(
            tenantKey = xTenantKey,
            userKey = user.key,
            subscriptionKey = subscriptionKey,
            subscriptionPatch = subscriptionDomainDto,
        )

        return ResponseEntity.ok(webPortalSubscriptionMapper.toWebPortalSubscriptionDetailsResponse(updatedSubscription))
    }

    @Transactional
    override suspend fun deleteSubscription(
        xTenantKey: String,
        subscriptionKey: String,
    ): ResponseEntity<Unit> {
        webPortalTenantAccessAllowedChecker.throwExceptionIfNotAuthorizedForCurrentUser(xTenantKey, Right.CUSTOMERS_WRITE)

        subscriptionDomainApiService.deleteSubscription(xTenantKey, subscriptionKey)

        return ResponseEntity.noContent().build()
    }
}
