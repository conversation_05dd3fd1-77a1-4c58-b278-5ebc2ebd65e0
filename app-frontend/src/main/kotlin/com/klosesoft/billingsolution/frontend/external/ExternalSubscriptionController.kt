package com.klosesoft.billingsolution.frontend.external

import com.klosesoft.billingsolution.common.utils.BillingSolutionConstants
import com.klosesoft.billingsolution.domain.logic.api.service.direct.SubscriptionDomainApiService
import com.klosesoft.billingsolution.domain.logic.api.service.load.SubscriptionLoadDomainApiService
import com.klosesoft.billingsolution.frontend.ControllerUtil.createValidPageable
import com.klosesoft.billingsolution.frontend.auth.TenantUtil
import com.klosesoft.billingsolution.frontend.external.mapper.ExternalSubscriptionMapper
import com.klosesoft.billingsolution.generated.api.external.server.SubscriptionsApi
import com.klosesoft.billingsolution.generated.api.external.server.model.ExternalSubscriptionDetailsDataDto
import com.klosesoft.billingsolution.generated.api.external.server.model.ExternalSubscriptionDetailsResponseDto
import com.klosesoft.billingsolution.generated.api.external.server.model.ExternalSubscriptionsPagedResponseDto
import org.springframework.http.ResponseEntity
import org.springframework.transaction.annotation.Transactional
import org.springframework.web.bind.annotation.RestController

@RestController
class ExternalSubscriptionController(
    private val subscriptionLoadDomainApiService: SubscriptionLoadDomainApiService,
    private val subscriptionDomainApiService: SubscriptionDomainApiService,
    private val externalSubscriptionMapper: ExternalSubscriptionMapper,
) : SubscriptionsApi {

    @Transactional(readOnly = true)
    override suspend fun loadSubscriptions(
        page: Int?,
        size: Int?,
        sort: String?,
        direction: String?,
        filter: String?,
    ): ResponseEntity<ExternalSubscriptionsPagedResponseDto> {
        val subscriptionsPage = subscriptionLoadDomainApiService.findAllSubscriptions(
            tenantKey = TenantUtil.getTenantKey(),
            rsqlFilter = filter,
            pageable = createValidPageable(page, size, sort, direction),
        )

        return ResponseEntity.ok(externalSubscriptionMapper.toExternalPagedResponse(subscriptionsPage))
    }

    @Transactional
    override suspend fun createSubscription(
        externalSubscriptionDetailsDataDto: ExternalSubscriptionDetailsDataDto,
    ): ResponseEntity<ExternalSubscriptionDetailsResponseDto> {
        val subscriptionDomainDto = externalSubscriptionMapper.toDomainDto(externalSubscriptionDetailsDataDto)

        val createdSubscription = subscriptionDomainApiService.createSubscription(
            tenantKey = TenantUtil.getTenantKey(),
            userKey = BillingSolutionConstants.SYSTEM_USER_KEY,
            subscriptionDomainDto = subscriptionDomainDto,
        )

        return ResponseEntity.status(201).body(
            externalSubscriptionMapper.toExternalSubscriptionDetailsResponse(createdSubscription)
        )
    }

    @Transactional(readOnly = true)
    override suspend fun loadSubscription(
        subscriptionKey: String,
    ): ResponseEntity<ExternalSubscriptionDetailsResponseDto> {
        val subscription = subscriptionLoadDomainApiService.findSubscription(TenantUtil.getTenantKey(), subscriptionKey)

        return ResponseEntity.ok(externalSubscriptionMapper.toExternalSubscriptionDetailsResponse(subscription))
    }

    @Transactional
    override suspend fun updateSubscription(
        subscriptionKey: String,
        externalSubscriptionDetailsDataDto: ExternalSubscriptionDetailsDataDto,
    ): ResponseEntity<ExternalSubscriptionDetailsResponseDto> {
        val subscriptionDomainDto = externalSubscriptionMapper.toDomainDto(externalSubscriptionDetailsDataDto)

        val updatedSubscription = subscriptionDomainApiService.updateSubscriptionData(
            tenantKey = TenantUtil.getTenantKey(),
            userKey = BillingSolutionConstants.SYSTEM_USER_KEY,
            subscriptionKey = subscriptionKey,
            subscriptionPatch = subscriptionDomainDto,
        )

        return ResponseEntity.ok(externalSubscriptionMapper.toExternalSubscriptionDetailsResponse(updatedSubscription))
    }

    @Transactional
    override suspend fun deleteSubscription(
        subscriptionKey: String,
    ): ResponseEntity<Unit> {
        subscriptionDomainApiService.deleteSubscription(TenantUtil.getTenantKey(), subscriptionKey)

        return ResponseEntity.noContent().build()
    }
}
