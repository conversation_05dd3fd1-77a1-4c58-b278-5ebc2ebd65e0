package com.klosesoft.billingsolution.frontend.external.mapper

import com.klosesoft.billingsolution.domain.model.ReactivePageImpl
import com.klosesoft.billingsolution.domain.model.dto.SubscriptionDomainDto
import com.klosesoft.billingsolution.domain.model.dto.SubscriptionItemRequestDomainDto
import com.klosesoft.billingsolution.domain.model.dto.SubscriptionItemResponseDomainDto
import com.klosesoft.billingsolution.domain.model.valueobject.Currency
import com.klosesoft.billingsolution.domain.model.valueobject.SubscriptionFrequency
import com.klosesoft.billingsolution.domain.model.valueobject.SubscriptionStatus
import com.klosesoft.billingsolution.generated.api.external.server.model.CurrencyDto
import com.klosesoft.billingsolution.generated.api.external.server.model.ExternalSubscriptionDetailsDataDto
import com.klosesoft.billingsolution.generated.api.external.server.model.ExternalSubscriptionDetailsResponseDto
import com.klosesoft.billingsolution.generated.api.external.server.model.ExternalSubscriptionItemRequestDto
import com.klosesoft.billingsolution.generated.api.external.server.model.ExternalSubscriptionItemResponseDto
import com.klosesoft.billingsolution.generated.api.external.server.model.ExternalSubscriptionItemsPagedResponseDto
import com.klosesoft.billingsolution.generated.api.external.server.model.ExternalSubscriptionListEntryDto
import com.klosesoft.billingsolution.generated.api.external.server.model.ExternalSubscriptionsPagedResponseDto
import com.klosesoft.billingsolution.generated.api.external.server.model.PageDataDto
import com.klosesoft.billingsolution.generated.api.external.server.model.SubscriptionFrequencyDto
import com.klosesoft.billingsolution.generated.api.external.server.model.SubscriptionStatusDto
import kotlinx.coroutines.flow.toList
import org.springframework.stereotype.Service
import java.time.OffsetDateTime
import java.time.ZoneId

@Service
class ExternalSubscriptionMapper {

    suspend fun toExternalPagedResponse(
        subscriptionsPage: ReactivePageImpl<SubscriptionDomainDto>,
    ): ExternalSubscriptionsPagedResponseDto = ExternalSubscriptionsPagedResponseDto(
        pageData = PageDataDto(totalCount = subscriptionsPage.totalElements, pageIndex = subscriptionsPage.number),
        content = subscriptionsPage.map { subscription -> this.toExternalListDto(subscription) }.content.toList(),
    )

    suspend fun toExternalListDto(
        subscription: SubscriptionDomainDto,
    ): ExternalSubscriptionListEntryDto = ExternalSubscriptionListEntryDto(
        key = subscription.key,
        name = subscription.name,
        customerKey = subscription.customerKey,
        status = SubscriptionStatusDto.valueOf(subscription.status.name),
        frequency = SubscriptionFrequencyDto.valueOf(subscription.frequency.name),
        amount = subscription.amount,
        currency = CurrencyDto.valueOf(subscription.currency.name),
        nextBillingDate = subscription.nextBillingDate,
        createdAt = subscription.startDate.atStartOfDay().atZone(ZoneId.of("UTC")).toOffsetDateTime(),
    )

    suspend fun toExternalSubscriptionDetailsResponse(
        subscription: SubscriptionDomainDto,
    ): ExternalSubscriptionDetailsResponseDto = ExternalSubscriptionDetailsResponseDto(
        subscriptionData = toExternalSubscriptionDetailsData(subscription),
    )

    private fun toExternalSubscriptionDetailsData(
        subscription: SubscriptionDomainDto,
    ): ExternalSubscriptionDetailsDataDto = ExternalSubscriptionDetailsDataDto(
        key = subscription.key,
        name = subscription.name,
        description = subscription.description,
        customerKey = subscription.customerKey,
        status = SubscriptionStatusDto.valueOf(subscription.status.name),
        frequency = SubscriptionFrequencyDto.valueOf(subscription.frequency.name),
        amount = subscription.amount,
        currency = CurrencyDto.valueOf(subscription.currency.name),
        startDate = subscription.startDate,
        endDate = subscription.endDate,
        nextBillingDate = subscription.nextBillingDate,
        version = subscription.version,
    )

    fun toDomainDto(
        externalSubscriptionDetailsDataDto: ExternalSubscriptionDetailsDataDto,
    ): SubscriptionDomainDto = SubscriptionDomainDto(
        key = externalSubscriptionDetailsDataDto.key,
        name = externalSubscriptionDetailsDataDto.name,
        description = externalSubscriptionDetailsDataDto.description,
        customerKey = externalSubscriptionDetailsDataDto.customerKey,
        status = SubscriptionStatus.valueOf(externalSubscriptionDetailsDataDto.status.name),
        frequency = SubscriptionFrequency.valueOf(externalSubscriptionDetailsDataDto.frequency.name),
        amount = externalSubscriptionDetailsDataDto.amount,
        currency = Currency.valueOf(externalSubscriptionDetailsDataDto.currency.name),
        startDate = externalSubscriptionDetailsDataDto.startDate,
        endDate = externalSubscriptionDetailsDataDto.endDate,
        nextBillingDate = externalSubscriptionDetailsDataDto.nextBillingDate,
        version = externalSubscriptionDetailsDataDto.version ?: 0,
    )

    fun toDomainDto(
        externalSubscriptionItemRequestDto: ExternalSubscriptionItemRequestDto,
    ): SubscriptionItemRequestDomainDto = SubscriptionItemRequestDomainDto(
        articleNumber = externalSubscriptionItemRequestDto.articleNumber,
        debitCreditIndicator = ExternalMapperUtil.toDomain(externalSubscriptionItemRequestDto.debitCreditIndicator),
        category = ExternalMapperUtil.toDomain(externalSubscriptionItemRequestDto.category),
        itemGroup = externalSubscriptionItemRequestDto.itemGroup,
        name = externalSubscriptionItemRequestDto.name,
        description = externalSubscriptionItemRequestDto.description,
        supplierKey = externalSubscriptionItemRequestDto.supplierKey,
        quantity = externalSubscriptionItemRequestDto.quantity,
        currency = ExternalMapperUtil.toDomain(externalSubscriptionItemRequestDto.currency),
        unitNetAmount = externalSubscriptionItemRequestDto.unitNetAmount,
        taxes = ExternalMapperUtil.toDomain(externalSubscriptionItemRequestDto.taxes),
        properties = ExternalMapperUtil.toDomain(externalSubscriptionItemRequestDto.properties),
        key = externalSubscriptionItemRequestDto.key,
        version = externalSubscriptionItemRequestDto.version ?: 0,
    )

    fun toExternal(
        subscriptionItemResponse: SubscriptionItemResponseDomainDto,
    ): ExternalSubscriptionItemResponseDto = ExternalSubscriptionItemResponseDto(
        key = subscriptionItemResponse.key,
        version = subscriptionItemResponse.version,
        articleNumber = subscriptionItemResponse.articleNumber,
        itemGroup = subscriptionItemResponse.itemGroup,
        category = ExternalMapperUtil.toExternal(subscriptionItemResponse.category),
        debitCreditIndicator = ExternalMapperUtil.toExternal(subscriptionItemResponse.debitCreditIndicator),
        supplierKey = subscriptionItemResponse.supplierKey,
        name = subscriptionItemResponse.name,
        description = subscriptionItemResponse.description,
        quantity = subscriptionItemResponse.quantity,
        unitNetAmount = subscriptionItemResponse.unitNetAmount,
        totalNetAmount = subscriptionItemResponse.totalNetAmount,
        currency = ExternalMapperUtil.toExternal(subscriptionItemResponse.currency),
        properties = ExternalMapperUtil.toExternal(subscriptionItemResponse.properties),
        taxes = ExternalMapperUtil.toExternal(subscriptionItemResponse.taxes),
    )

    suspend fun toExternalSubscriptionItemsPagedResponse(
        subscriptionItemsPage: ReactivePageImpl<SubscriptionItemResponseDomainDto>,
    ): ExternalSubscriptionItemsPagedResponseDto = ExternalSubscriptionItemsPagedResponseDto(
        pageData = PageDataDto(totalCount = subscriptionItemsPage.totalElements, pageIndex = subscriptionItemsPage.number),
        items = subscriptionItemsPage.map { toExternal(it) }.content.toList(),
    )
}
