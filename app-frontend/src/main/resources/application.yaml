spring:
  jackson:
    default-property-inclusion: non_null
  liquibase:
    change-log: classpath:db/changelog/db.changelog-master.yaml
    enabled: true
  datasource:
    url: ************************************************
    driver-class-name: org.postgresql.Driver
    username: user
    password: pass
  r2dbc:
    url: r2dbc:postgresql://localhost:5432/billingsolution
    username: user
    password: pass
    pool:
      initial-size: 50
      max-size: 200
      max-idle-time: 30m
  cache:
    type: caffeine
    caffeine:
      spec: maximumSize=100,expireAfterWrite=5m
  http:
    codecs:
      max-in-memory-size: 10MB

flowable:
  process-definition-location-prefix: classpath:/dummy/
  deployment-mode: none
  database-schema-update: true
  async-executor-activate: true
  process:
    default-failed-job-retry-time-cycle: 1

management:
  endpoint:
    health:
      probes:
        enabled: true

frontend:
  webportal:
    app-client-id: 5m3qjrep368abdt2t74akl7dul
  cognito:
    user-pool-id: eu-central-1_SRIB1Yci6
    access-key-id: ********************
    access-key-secret: zr7UeSRsUseA0eVkU+QscBRMKB7wa8z7UD/anGuK
  s3:
    url: http://localhost:4566
    bucket-name: bucket
    access-key-id: access-key
    access-key-secret: access-secret
  netty:
    numThreads: 20

mail:
  from: <EMAIL>
  username: o2c
  password: o2c
  smtp:
    host: host.docker.internal
    port: 3025
    localhost: localhost
