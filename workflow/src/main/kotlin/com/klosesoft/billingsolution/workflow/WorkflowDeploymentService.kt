package com.klosesoft.billingsolution.workflow

import com.klosesoft.billingsolution.persistence.service.TenantLoadService
import io.github.oshai.kotlinlogging.KotlinLogging
import kotlinx.coroutines.flow.toList
import kotlinx.coroutines.runBlocking
import org.flowable.engine.RepositoryService
import org.springframework.boot.context.event.ApplicationReadyEvent
import org.springframework.context.event.EventListener
import org.springframework.core.io.support.PathMatchingResourcePatternResolver
import org.springframework.stereotype.Service
import java.util.Locale

@Service
class WorkflowDeploymentService(private val repositoryService: RepositoryService, private val tenantLoadService: TenantLoadService) {

    private val resourcePatternResolver = PathMatchingResourcePatternResolver()

    val logger = KotlinLogging.logger {}

    @EventListener(ApplicationReadyEvent::class)
    fun deployWorkflows() {
        runBlocking {
            val tenants = tenantLoadService.findAll().toList()

            tenants.forEach { tenant ->
                tenantLoadService.findTenantConfigByTenantId(tenant.id).let { tenantConfig ->
                    // Deploy order workflow
                    val orderWorkflowDefinition =
                        tenantConfig.orderWorkflow.replaceFirstChar {
                            if (it.isLowerCase()) {
                                it.titlecase(Locale.getDefault())
                            } else {
                                it.toString()
                            }
                        }

                    val orderResource = resourcePatternResolver.getResource("classpath:/processes/$orderWorkflowDefinition.bpmn")
                    val orderResourceName = orderResource.filename ?: return@forEach

                    val orderDeployment = repositoryService.createDeployment()
                    orderResource.inputStream.use { inputStream ->
                        orderDeployment.addInputStream(orderResourceName, inputStream)
                        orderDeployment.name(orderResourceName)
                        orderDeployment.tenantId(tenant.key)
                        orderDeployment.deploy()
                    }
                    logger.info { "Deployed order workflow '$orderResourceName' for tenant '${tenant.key}'" }

                    // Deploy subscription workflow
                    val subscriptionWorkflowDefinition =
                        tenantConfig.subscriptionWorkflow.replaceFirstChar {
                            if (it.isLowerCase()) {
                                it.titlecase(Locale.getDefault())
                            } else {
                                it.toString()
                            }
                        }

                    val subscriptionResource = resourcePatternResolver
                        .getResource("classpath:/processes/$subscriptionWorkflowDefinition.bpmn")
                    if (subscriptionResource.exists()) {
                        val subscriptionResourceName = subscriptionResource.filename ?: return@forEach

                        val subscriptionDeployment = repositoryService.createDeployment()
                        subscriptionResource.inputStream.use { inputStream ->
                            subscriptionDeployment.addInputStream(subscriptionResourceName, inputStream)
                            subscriptionDeployment.name(subscriptionResourceName)
                            subscriptionDeployment.tenantId(tenant.key)
                            subscriptionDeployment.deploy()
                        }
                        logger.info { "Deployed subscription workflow '$subscriptionResourceName' for tenant '${tenant.key}'" }
                    } else {
                        logger.warn { "Subscription workflow '$subscriptionWorkflowDefinition.bpmn' not found for tenant '${tenant.key}'" }
                    }
                }
            }
        }
    }
}
