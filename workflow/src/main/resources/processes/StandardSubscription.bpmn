<?xml version="1.0" encoding="UTF-8"?>
<definitions xmlns="http://www.omg.org/spec/BPMN/20100524/MODEL"
             xmlns:bpmndi="http://www.omg.org/spec/BPMN/20100524/DI"
             xmlns:omgdc="http://www.omg.org/spec/DD/20100524/DC"
             xmlns:omgdi="http://www.omg.org/spec/DD/20100524/DI"
             xmlns:flowable="http://flowable.org/bpmn"
             xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
             typeLanguage="http://www.w3.org/2001/XMLSchema"
             expressionLanguage="http://www.w3.org/1999/XPath"
             targetNamespace="http://www.flowable.org/processdef">

  <message id="pauseSubscriptionMessage" name="pauseSubscription"/>
  <message id="resumeSubscriptionMessage" name="resumeSubscription"/>
  <message id="cancelSubscriptionMessage" name="cancelSubscription"/>
  <message id="processSubscriptionBillingMessage" name="processSubscriptionBilling"/>

  <process id="standardSubscription" name="Standard Subscription" isExecutable="true">

    <!-- Start Event -->
    <startEvent id="startEvent" name="Start"/>

    <!-- Create Subscription -->
    <serviceTask id="createSubscription" name="Create Subscription" flowable:delegateExpression="${createSubscription}"/>
    <sequenceFlow id="flow1" sourceRef="startEvent" targetRef="createSubscription"/>

    <!-- Activate Subscription -->
    <serviceTask id="activateSubscription" name="Activate Subscription" flowable:delegateExpression="${activateSubscription}"/>
    <sequenceFlow id="flow2" sourceRef="createSubscription" targetRef="activateSubscription"/>

    <!-- Billing Process -->
    <serviceTask id="processSubscriptionBilling" name="Process Subscription Billing" flowable:async="true" flowable:delegateExpression="${processSubscriptionBilling}"/>
    <sequenceFlow id="flow3" sourceRef="activateSubscription" targetRef="processSubscriptionBilling"/>

    <!-- Create Invoice -->
    <serviceTask id="createSubscriptionInvoice" name="Create Subscription Invoice" flowable:async="true" flowable:delegateExpression="${createSubscriptionInvoice}"/>
    <sequenceFlow id="flow4" sourceRef="processSubscriptionBilling" targetRef="createSubscriptionInvoice"/>

    <!-- Send Invoice -->
    <serviceTask id="sendSubscriptionInvoice" name="Send Subscription Invoice" flowable:async="true" flowable:delegateExpression="${sendSubscriptionInvoice}"/>
    <sequenceFlow id="flow5" sourceRef="createSubscriptionInvoice" targetRef="sendSubscriptionInvoice"/>

    <!-- Wait for Events -->
    <eventBasedGateway id="eventGateway" name="Event Gateway"/>
    <sequenceFlow id="flow6" sourceRef="sendSubscriptionInvoice" targetRef="eventGateway"/>

    <!-- Billing Event -->
    <intermediateCatchEvent id="billingEvent" name="Billing Event">
      <messageEventDefinition messageRef="processSubscriptionBillingMessage"/>
    </intermediateCatchEvent>
    <sequenceFlow id="flow7" sourceRef="eventGateway" targetRef="billingEvent"/>
    <sequenceFlow id="flow8" sourceRef="billingEvent" targetRef="processSubscriptionBilling"/>

    <!-- Pause Event -->
    <intermediateCatchEvent id="pauseEvent" name="Pause Event">
      <messageEventDefinition messageRef="pauseSubscriptionMessage"/>
    </intermediateCatchEvent>
    <sequenceFlow id="flow9" sourceRef="eventGateway" targetRef="pauseEvent"/>

    <!-- Pause Task -->
    <serviceTask id="pauseSubscription" name="Pause Subscription" flowable:delegateExpression="${pauseSubscription}"/>
    <sequenceFlow id="flow10" sourceRef="pauseEvent" targetRef="pauseSubscription"/>

    <!-- Resume Event -->
    <intermediateCatchEvent id="resumeEvent" name="Resume Event">
      <messageEventDefinition messageRef="resumeSubscriptionMessage"/>
    </intermediateCatchEvent>
    <sequenceFlow id="flow11" sourceRef="pauseSubscription" targetRef="resumeEvent"/>

    <!-- Resume Task -->
    <serviceTask id="resumeSubscription" name="Resume Subscription" flowable:delegateExpression="${resumeSubscription}"/>
    <sequenceFlow id="flow12" sourceRef="resumeEvent" targetRef="resumeSubscription"/>
    <sequenceFlow id="flow13" sourceRef="resumeSubscription" targetRef="eventGateway"/>

    <!-- Cancel Event -->
    <intermediateCatchEvent id="cancelEvent" name="Cancel Event">
      <messageEventDefinition messageRef="cancelSubscriptionMessage"/>
    </intermediateCatchEvent>
    <sequenceFlow id="flow14" sourceRef="eventGateway" targetRef="cancelEvent"/>

    <!-- Cancel Task -->
    <serviceTask id="cancelSubscription" name="Cancel Subscription" flowable:delegateExpression="${cancelSubscription}"/>
    <sequenceFlow id="flow15" sourceRef="cancelEvent" targetRef="cancelSubscription"/>

    <!-- End Event -->
    <endEvent id="endEvent" name="End"/>
    <sequenceFlow id="flow16" sourceRef="cancelSubscription" targetRef="endEvent"/>

  </process>

  <bpmndi:BPMNDiagram id="BPMNDiagram_standardSubscription">
    <bpmndi:BPMNPlane bpmnElement="standardSubscription" id="BPMNPlane_standardSubscription">

      <!-- Start Event -->
      <bpmndi:BPMNShape bpmnElement="startEvent" id="BPMNShape_startEvent">
        <omgdc:Bounds height="35.0" width="35.0" x="50.0" y="200.0"/>
      </bpmndi:BPMNShape>

      <!-- Create Subscription -->
      <bpmndi:BPMNShape bpmnElement="createSubscription" id="BPMNShape_createSubscription">
        <omgdc:Bounds height="80.0" width="100.0" x="150.0" y="177.5"/>
      </bpmndi:BPMNShape>

      <!-- Activate Subscription -->
      <bpmndi:BPMNShape bpmnElement="activateSubscription" id="BPMNShape_activateSubscription">
        <omgdc:Bounds height="80.0" width="100.0" x="300.0" y="177.5"/>
      </bpmndi:BPMNShape>

      <!-- Process Subscription Billing -->
      <bpmndi:BPMNShape bpmnElement="processSubscriptionBilling" id="BPMNShape_processSubscriptionBilling">
        <omgdc:Bounds height="80.0" width="100.0" x="450.0" y="177.5"/>
      </bpmndi:BPMNShape>

      <!-- Create Subscription Invoice -->
      <bpmndi:BPMNShape bpmnElement="createSubscriptionInvoice" id="BPMNShape_createSubscriptionInvoice">
        <omgdc:Bounds height="80.0" width="100.0" x="600.0" y="177.5"/>
      </bpmndi:BPMNShape>

      <!-- Send Subscription Invoice -->
      <bpmndi:BPMNShape bpmnElement="sendSubscriptionInvoice" id="BPMNShape_sendSubscriptionInvoice">
        <omgdc:Bounds height="80.0" width="100.0" x="750.0" y="177.5"/>
      </bpmndi:BPMNShape>

      <!-- Event Gateway -->
      <bpmndi:BPMNShape bpmnElement="eventGateway" id="BPMNShape_eventGateway">
        <omgdc:Bounds height="50.0" width="50.0" x="900.0" y="192.5"/>
      </bpmndi:BPMNShape>

      <!-- Billing Event -->
      <bpmndi:BPMNShape bpmnElement="billingEvent" id="BPMNShape_billingEvent">
        <omgdc:Bounds height="35.0" width="35.0" x="1000.0" y="100.0"/>
      </bpmndi:BPMNShape>

      <!-- Pause Event -->
      <bpmndi:BPMNShape bpmnElement="pauseEvent" id="BPMNShape_pauseEvent">
        <omgdc:Bounds height="35.0" width="35.0" x="1000.0" y="200.0"/>
      </bpmndi:BPMNShape>

      <!-- Pause Subscription -->
      <bpmndi:BPMNShape bpmnElement="pauseSubscription" id="BPMNShape_pauseSubscription">
        <omgdc:Bounds height="80.0" width="100.0" x="1100.0" y="177.5"/>
      </bpmndi:BPMNShape>

      <!-- Resume Event -->
      <bpmndi:BPMNShape bpmnElement="resumeEvent" id="BPMNShape_resumeEvent">
        <omgdc:Bounds height="35.0" width="35.0" x="1250.0" y="200.0"/>
      </bpmndi:BPMNShape>

      <!-- Resume Subscription -->
      <bpmndi:BPMNShape bpmnElement="resumeSubscription" id="BPMNShape_resumeSubscription">
        <omgdc:Bounds height="80.0" width="100.0" x="1350.0" y="177.5"/>
      </bpmndi:BPMNShape>

      <!-- Cancel Event -->
      <bpmndi:BPMNShape bpmnElement="cancelEvent" id="BPMNShape_cancelEvent">
        <omgdc:Bounds height="35.0" width="35.0" x="1000.0" y="300.0"/>
      </bpmndi:BPMNShape>

      <!-- Cancel Subscription -->
      <bpmndi:BPMNShape bpmnElement="cancelSubscription" id="BPMNShape_cancelSubscription">
        <omgdc:Bounds height="80.0" width="100.0" x="1100.0" y="277.5"/>
      </bpmndi:BPMNShape>

      <!-- End Event -->
      <bpmndi:BPMNShape bpmnElement="endEvent" id="BPMNShape_endEvent">
        <omgdc:Bounds height="35.0" width="35.0" x="1250.0" y="300.0"/>
      </bpmndi:BPMNShape>

      <!-- Sequence Flows -->

      <!-- flow1: Start to Create Subscription -->
      <bpmndi:BPMNEdge bpmnElement="flow1" id="BPMNEdge_flow1">
        <omgdi:waypoint x="85.0" y="217.5"/>
        <omgdi:waypoint x="150.0" y="217.5"/>
      </bpmndi:BPMNEdge>

      <!-- flow2: Create to Activate Subscription -->
      <bpmndi:BPMNEdge bpmnElement="flow2" id="BPMNEdge_flow2">
        <omgdi:waypoint x="250.0" y="217.5"/>
        <omgdi:waypoint x="300.0" y="217.5"/>
      </bpmndi:BPMNEdge>

      <!-- flow3: Activate to Process Billing -->
      <bpmndi:BPMNEdge bpmnElement="flow3" id="BPMNEdge_flow3">
        <omgdi:waypoint x="400.0" y="217.5"/>
        <omgdi:waypoint x="450.0" y="217.5"/>
      </bpmndi:BPMNEdge>

      <!-- flow4: Process Billing to Create Invoice -->
      <bpmndi:BPMNEdge bpmnElement="flow4" id="BPMNEdge_flow4">
        <omgdi:waypoint x="550.0" y="217.5"/>
        <omgdi:waypoint x="600.0" y="217.5"/>
      </bpmndi:BPMNEdge>

      <!-- flow5: Create Invoice to Send Invoice -->
      <bpmndi:BPMNEdge bpmnElement="flow5" id="BPMNEdge_flow5">
        <omgdi:waypoint x="700.0" y="217.5"/>
        <omgdi:waypoint x="750.0" y="217.5"/>
      </bpmndi:BPMNEdge>

      <!-- flow6: Send Invoice to Event Gateway -->
      <bpmndi:BPMNEdge bpmnElement="flow6" id="BPMNEdge_flow6">
        <omgdi:waypoint x="850.0" y="217.5"/>
        <omgdi:waypoint x="900.0" y="217.5"/>
      </bpmndi:BPMNEdge>

      <!-- flow7: Event Gateway to Billing Event -->
      <bpmndi:BPMNEdge bpmnElement="flow7" id="BPMNEdge_flow7">
        <omgdi:waypoint x="925.0" y="192.5"/>
        <omgdi:waypoint x="925.0" y="117.5"/>
        <omgdi:waypoint x="1000.0" y="117.5"/>
      </bpmndi:BPMNEdge>

      <!-- flow8: Billing Event back to Process Billing -->
      <bpmndi:BPMNEdge bpmnElement="flow8" id="BPMNEdge_flow8">
        <omgdi:waypoint x="1017.5" y="100.0"/>
        <omgdi:waypoint x="1017.5" y="50.0"/>
        <omgdi:waypoint x="500.0" y="50.0"/>
        <omgdi:waypoint x="500.0" y="177.5"/>
      </bpmndi:BPMNEdge>

      <!-- flow9: Event Gateway to Pause Event -->
      <bpmndi:BPMNEdge bpmnElement="flow9" id="BPMNEdge_flow9">
        <omgdi:waypoint x="950.0" y="217.5"/>
        <omgdi:waypoint x="1000.0" y="217.5"/>
      </bpmndi:BPMNEdge>

      <!-- flow10: Pause Event to Pause Subscription -->
      <bpmndi:BPMNEdge bpmnElement="flow10" id="BPMNEdge_flow10">
        <omgdi:waypoint x="1035.0" y="217.5"/>
        <omgdi:waypoint x="1100.0" y="217.5"/>
      </bpmndi:BPMNEdge>

      <!-- flow11: Pause Subscription to Resume Event -->
      <bpmndi:BPMNEdge bpmnElement="flow11" id="BPMNEdge_flow11">
        <omgdi:waypoint x="1200.0" y="217.5"/>
        <omgdi:waypoint x="1250.0" y="217.5"/>
      </bpmndi:BPMNEdge>

      <!-- flow12: Resume Event to Resume Subscription -->
      <bpmndi:BPMNEdge bpmnElement="flow12" id="BPMNEdge_flow12">
        <omgdi:waypoint x="1285.0" y="217.5"/>
        <omgdi:waypoint x="1350.0" y="217.5"/>
      </bpmndi:BPMNEdge>

      <!-- flow13: Resume Subscription back to Event Gateway -->
      <bpmndi:BPMNEdge bpmnElement="flow13" id="BPMNEdge_flow13">
        <omgdi:waypoint x="1400.0" y="177.5"/>
        <omgdi:waypoint x="1400.0" y="150.0"/>
        <omgdi:waypoint x="925.0" y="150.0"/>
        <omgdi:waypoint x="925.0" y="192.5"/>
      </bpmndi:BPMNEdge>

      <!-- flow14: Event Gateway to Cancel Event -->
      <bpmndi:BPMNEdge bpmnElement="flow14" id="BPMNEdge_flow14">
        <omgdi:waypoint x="925.0" y="242.5"/>
        <omgdi:waypoint x="925.0" y="317.5"/>
        <omgdi:waypoint x="1000.0" y="317.5"/>
      </bpmndi:BPMNEdge>

      <!-- flow15: Cancel Event to Cancel Subscription -->
      <bpmndi:BPMNEdge bpmnElement="flow15" id="BPMNEdge_flow15">
        <omgdi:waypoint x="1035.0" y="317.5"/>
        <omgdi:waypoint x="1100.0" y="317.5"/>
      </bpmndi:BPMNEdge>

      <!-- flow16: Cancel Subscription to End Event -->
      <bpmndi:BPMNEdge bpmnElement="flow16" id="BPMNEdge_flow16">
        <omgdi:waypoint x="1200.0" y="317.5"/>
        <omgdi:waypoint x="1250.0" y="317.5"/>
      </bpmndi:BPMNEdge>

    </bpmndi:BPMNPlane>
  </bpmndi:BPMNDiagram>

</definitions>
