package com.klosesoft.billingsolution.workflow

import org.flowable.bpmn.converter.BpmnXMLConverter
import org.junit.jupiter.api.Assertions.assertTrue
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertNotNull
import org.springframework.core.io.ClassPathResource
import javax.xml.stream.XMLInputFactory

class BpmnValidationTest {

    private val bpmnConverter = BpmnXMLConverter()
    private val xmlInputFactory = XMLInputFactory.newInstance()

    @Test
    fun `should parse StandardSubscription BPMN without errors`() {
        val resource = ClassPathResource("processes/StandardSubscription.bpmn")
        assertTrue(resource.exists(), "StandardSubscription.bpmn should exist")

        resource.inputStream.use { inputStream ->
            val xmlStreamReader = xmlInputFactory.createXMLStreamReader(inputStream)
            val bpmnModel = bpmnConverter.convertToBpmnModel(xmlStreamReader)
            assertNotNull(bpmnModel, "BPMN model should be parsed successfully")
            assertTrue(bpmnModel.processes.isNotEmpty(), "BPMN model should contain at least one process")

            val process = bpmnModel.processes.first()
            assertNotNull(process, "Process should not be null")
            println("Successfully parsed StandardSubscription.bpmn with process: ${process.id}")
        }
    }

    @Test
    fun `should parse VelocityWheelsSubscription BPMN without errors`() {
        val resource = ClassPathResource("processes/VelocityWheelsSubscription.bpmn")
        assertTrue(resource.exists(), "VelocityWheelsSubscription.bpmn should exist")

        resource.inputStream.use { inputStream ->
            val xmlStreamReader = xmlInputFactory.createXMLStreamReader(inputStream)
            val bpmnModel = bpmnConverter.convertToBpmnModel(xmlStreamReader)
            assertNotNull(bpmnModel, "BPMN model should be parsed successfully")
            assertTrue(bpmnModel.processes.isNotEmpty(), "BPMN model should contain at least one process")

            val process = bpmnModel.processes.first()
            assertNotNull(process, "Process should not be null")
            println("Successfully parsed VelocityWheelsSubscription.bpmn with process: ${process.id}")
        }
    }

    @Test
    fun `should parse all BPMN files without duplicate ID errors`() {
        val bpmnFiles = listOf(
            "processes/StandardSubscription.bpmn",
            "processes/VelocityWheelsSubscription.bpmn",
            "processes/StandardOrder.bpmn",
            "processes/BikeSaleUkOrder.bpmn",
            "processes/MvzGesundOrder.bpmn"
        )

        bpmnFiles.forEach { bpmnFile ->
            val resource = ClassPathResource(bpmnFile)
            assertTrue(resource.exists(), "$bpmnFile should exist")

            resource.inputStream.use { inputStream ->
                val xmlStreamReader = xmlInputFactory.createXMLStreamReader(inputStream)
                val bpmnModel = bpmnConverter.convertToBpmnModel(xmlStreamReader)
                assertNotNull(bpmnModel, "$bpmnFile should be parsed successfully")
                assertTrue(bpmnModel.processes.isNotEmpty(), "$bpmnFile should contain at least one process")

                val process = bpmnModel.processes.first()
                assertNotNull(process, "Process in $bpmnFile should not be null")
                println("Successfully parsed $bpmnFile with process: ${process.id}")
            }
        }
    }
}
