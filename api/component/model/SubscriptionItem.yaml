type: object
properties:
  key:
    type: string
    description: The key of the subscription item
  articleNumber:
    type: string
    description: The article number of the subscription item
  itemGroup:
    type: string
    description: The item group of the subscription item
  category:
    $ref: './ItemCategory.yaml'
  debitCreditIndicator:
    $ref: './DebitCreditIndicator.yaml'
  name:
    type: string
    description: The name of the subscription item
  description:
    type: string
    description: The description of the subscription item
  quantity:
    type: integer
    format: int32
    description: The quantity of the subscription item
  unitNetAmount:
    type: number
    description: The unit price amount of the subscription item
  currency:
    $ref: './Currency.yaml'
  properties:
    type: object
    description: Additional properties for the subscription item
    additionalProperties:
      type: string
required:
  - key
  - articleNumber
  - category
  - debitCreditIndicator
  - name
  - quantity
  - unitNetAmount
  - currency
