type: object
properties:
  key:
    type: string
    description: The key of the subscription item
  version:
    type: integer
    format: int64
    description: The version of the subscription item
  articleNumber:
    type: string
    description: The article number of the subscription item
  itemGroup:
    type: string
    description: The item group of the subscription item
  category:
    $ref: '../../model/ItemCategory.yaml'
  debitCreditIndicator:
    $ref: '../../model/DebitCreditIndicator.yaml'
  supplierKey:
    type: string
    description: The supplier key of the subscription item
  name:
    type: string
    description: The name of the subscription item
  description:
    type: string
    description: The description of the subscription item
  quantity:
    type: number
    description: The quantity of the subscription item
  unitNetAmount:
    type: number
    description: The unit net amount of the subscription item
  totalNetAmount:
    type: number
    description: The total net amount of the subscription item
  currency:
    $ref: '../../model/Currency.yaml'
  properties:
    type: array
    items:
      $ref: '../../model/Property.yaml'
  taxInformation:
    $ref: '../../model/TaxInformation.yaml'
required:
  - key
  - version
  - articleNumber
  - category
  - debitCreditIndicator
  - name
  - quantity
  - unitNetAmount
  - currency
  - taxInformation
