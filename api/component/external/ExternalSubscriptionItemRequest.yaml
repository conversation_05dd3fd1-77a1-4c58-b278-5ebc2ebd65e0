type: object
properties:
  key:
    type: string
    description: The key of the subscription item
  articleNumber:
    type: string
    description: The article number of the subscription item
  itemGroup:
    type: string
    description: The item group of the subscription item
  category:
    $ref: '../model/ItemCategory.yaml'
  debitCreditIndicator:
    $ref: '../model/DebitCreditIndicator.yaml'
  name:
    type: string
    description: The name of the subscription item
  description:
    type: string
    description: The description of the subscription item
  quantity:
    type: number
    description: The quantity of the subscription item
  unitNetAmount:
    type: number
    description: The unit price amount of the subscription item
  currency:
    $ref: '../model/Currency.yaml'
  properties:
    type: array
    items:
      $ref: '../model/Property.yaml'
  taxes:
    type: array
    items:
      $ref: '../model/TaxInformation.yaml'
required:
  - key
  - articleNumber
  - category
  - debitCreditIndicator
  - name
  - quantity
  - unitNetAmount
  - currency
