openapi: "3.0.3"
info:
  version: 0.2.3
  title: Billing Solution API
servers:
  - url: https://api.klosesoft.com/
x-tagGroups:
  - name: Billing
    tags:
      - Customers
      - Suppliers
      - Orders
      - Balance Cases
      - Documents
      - Reports
      - Approvals
      - Business Segments
      - Subscriptions
  - name: Payments
    tags:
      - Payment Accounts
      - Payment Categorization Rules
      - Settlement Reports
      - Payment Transactions
      - Payment Assignments
  - name: Bookkeeping
    tags:
      - Booking Accounts
      - Booking Rules
      - Currency Exchange Rates
      - Posting Records

tags:
  - name: Customers
    description: Operations about customers
  - name: Suppliers
    description: Operations about suppliers
  - name: Orders
    description: Operations about orders
  - name: Balance Cases
    description: Operations about balance cases
  - name: Documents
    description: Operations about documents
  - name: Reports
    description: Operations about reports
  - name: Approvals
    description: Operations about approvals
  - name: Business Segments
    description: Operations about business segments
  - name: Subscriptions
    description: Operations about subscriptions

  - name: Payment Accounts
    description: Operations about payment accounts
  - name: Payment Categorization Rules
    description: Operations about payment categorization rules
  - name: Settlement Reports
    description: Operations about settlement reports
  - name: Payment Transactions
    description: Operations about payment transactions
  - name: Payment Assignments
    description: Operations about payment payment assignments

  - name: Booking Accounts
    description: Operations about booking accounts
  - name: Booking Rules
    description: Operations about booking rules
  - name: Currency Exchange Rates
    description: Operations about currency exchange rates
  - name: Posting Records
    description: Operations about posting records

paths:
  /external/v1/customers:
    get:
      tags:
        - Customers
      summary: Load all customers
      operationId: loadCustomers
      parameters:
        - name: page
          in: query
          description: The index of the page to retrieve
          schema:
            type: integer
            format: int32
        - name: size
          in: query
          description: The number of items per page
          schema:
            type: integer
            format: int32
        - name: sort
          in: query
          description: The field to sort the orders by. Supported values are 'createdAt', 'firstName', 'lastName', 'companyName'
          schema:
            type: string
        - name: direction
          in: query
          description: The direction to sort by. Supported values are 'asc', 'desc'
          schema:
            type: string
        - name: filter
          in: query
          description: The filter to apply on the customers. This can be any attribute of the customer.
          schema:
            type: string
      responses:
        '200':
          description: Page of customers
          content:
            application/json:
              schema:
                $ref: './component/external/customerlist/ExternalCustomersPagedResponse.yaml'
    post:
      tags:
        - Customers
      summary: Create a new customer
      operationId: createCustomer
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: './component/external/customerdetails/ExternalCustomerDetailsData.yaml'
      responses:
        '201':
          description: The customer was successfully created
          content:
            application/json:
              schema:
                $ref: './component/external/customerdetails/ExternalCustomerDetailsResponse.yaml'
        '400':
          description: Invalid input
        '404':
          description: Customer not found
  /external/v1/customers/{customer_key}:
    get:
      tags:
        - Customers
      operationId: loadCustomer
      summary: Get a specific customer
      parameters:
        - name: customer_key
          in: path
          required: true
          schema:
            type: string
      responses:
        '200':
          description: Successful operation
          content:
            application/json:
              schema:
                $ref: './component/external/customerdetails/ExternalCustomerDetailsResponse.yaml'
        '404':
          description: Customer not found
    put:
      tags:
        - Customers
      summary: Update a specific customer
      operationId: updateCustomer
      parameters:
        - name: customer_key
          in: path
          required: true
          schema:
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: './component/external/customerdetails/ExternalCustomerDetailsData.yaml'
      responses:
        '200':
          description: The customer was successfully updated
          content:
            application/json:
              schema:
                $ref: './component/external/customerdetails/ExternalCustomerDetailsResponse.yaml'
        '400':
          description: Invalid input
        '404':
          description: Customer not found
  /external/v1/customers/{customer_key}/billing-address:
    put:
      tags:
        - Customers
      summary: Update the billing address of a specific customer
      operationId: updateCustomerBillingAddress
      parameters:
        - name: customer_key
          in: path
          required: true
          schema:
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: './component/model/Address.yaml'
      responses:
        '200':
          description: Address was successfully updated
          content:
            application/json:
              schema:
                $ref: '././component/model/Address.yaml'
        '400':
          description: Invalid input
        '404':
          description: Customer not found
  /external/v1/customers/{customer_key}/shipping-addresses:
    get:
      tags:
        - Customers
      summary: Load all shipping addresses of a specific customer
      operationId: loadCustomerShippingAddresses
      parameters:
        - name: customer_key
          in: path
          required: true
          schema:
            type: string
        - name: page
          in: query
          description: The index of the page to retrieve
          schema:
            type: integer
            format: int32
        - name: size
          in: query
          description: The number of items per page
          schema:
            type: integer
            format: int32
        - name: sort
          in: query
          description: The field to sort the addresses by. Supported values are 'createdAt', 'addressLine1', 'city', 'state', 'country'
          schema:
            type: string
        - name: direction
          in: query
          description: The direction to sort by. Supported values are 'asc', 'desc'
          schema:
            type: string
        - name: filter
          in: query
          description: The filter to apply on the addresses. This can be any attribute of the address.
          schema:
            type: string
      responses:
        '200':
          description: Page of shipping addresses
          content:
            application/json:
              schema:
                $ref: './component/external/customerdetails/ExternalCustomerShippingAddressesPagedResponse.yaml'
    post:
      tags:
        - Customers
      summary: Creates a new shipping address for a specific customer
      operationId: addCustomerShippingAddress
      parameters:
        - name: customer_key
          in: path
          required: true
          schema:
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: './component/model/Address.yaml'
      responses:
        '201':
          description: The shipping address was successfully created
          content:
            application/json:
              schema:
                $ref: '././component/model/Address.yaml'
        '400':
          description: Invalid input
        '404':
          description: Customer not found
  /external/v1/customers/{customer_key}/shipping-addresses/{address_key}:
    get:
      tags:
        - Customers
      summary: Get a specific shipping address of a specific customer
      operationId: loadCustomerShippingAddress
      parameters:
        - name: customer_key
          in: path
          required: true
          schema:
            type: string
        - name: address_key
          in: path
          required: true
          schema:
            type: string
      responses:
        '200':
          description: Successful operation
          content:
            application/json:
              schema:
                $ref: './component/model/Address.yaml'
        '404':
          description: Customer or Address not found
    put:
      tags:
        - Customers
      summary: Update the shipping address of a specific customer
      operationId: updateCustomerShippingAddress
      parameters:
        - name: customer_key
          in: path
          required: true
          schema:
            type: string
        - name: address_key
          in: path
          required: true
          schema:
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: './component/model/Address.yaml'
      responses:
        '200':
          description: Address was successfully updated
          content:
            application/json:
              schema:
                $ref: '././component/model/Address.yaml'
        '400':
          description: Invalid input
        '404':
          description: Customer not found
  /external/v1/customers/{customer_key}/shipping-addresses/{address_key}/status:
    put:
      tags:
        - Customers
      summary: Update the status of a specific shipping address
      operationId: updateCustomerAddressStatus
      parameters:
        - name: x-tenant-key
          in: header
          description: The key of the tenant
          required: true
          schema:
            type: string
        - name: customer_key
          in: path
          required: true
          schema:
            type: string
        - name: address_key
          in: path
          required: true
          schema:
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: './component/external/customerdetails/ExternalCustomerDetailsCustomerAddressStatusUpdateRequest.yaml'
      responses:
        '200':
          description: Address was successfully updated
        '400':
          description: Invalid input
        '404':
          description: Customer not found
  /external/v1/suppliers:
    get:
      tags:
        - Suppliers
      summary: Load all suppliers
      operationId: loadSuppliers
      parameters:
        - name: page
          in: query
          description: The index of the page to retrieve
          schema:
            type: integer
            format: int32
        - name: size
          in: query
          description: The number of items per page
          schema:
            type: integer
            format: int32
        - name: sort
          in: query
          description: The field to sort the orders by. Supported values are 'createdAt', 'companyName'
          schema:
            type: string
        - name: direction
          in: query
          description: The direction to sort by. Supported values are 'asc', 'desc'
          schema:
            type: string
        - name: filter
          in: query
          description: The filter to apply on the suppliers. This can be any attribute of the supplier.
          schema:
            type: string
      responses:
        '200':
          description: Page of suppliers
          content:
            application/json:
              schema:
                $ref: './component/external/supplierlist/ExternalSuppliersPagedResponse.yaml'
    post:
      tags:
        - Suppliers
      summary: Create a new supplier
      operationId: createSupplier
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: './component/external/supplierdetails/ExternalSupplierDetailsData.yaml'
      responses:
        '201':
          description: The supplier was successfully created
          content:
            application/json:
              schema:
                $ref: './component/external/supplierdetails/ExternalSupplierDetailsResponse.yaml'
        '400':
          description: Invalid input
  /external/v1/suppliers/{supplier_key}:
    get:
      tags:
        - Suppliers
      operationId: loadSupplier
      summary: Get a specific supplier
      parameters:
        - name: supplier_key
          in: path
          required: true
          schema:
            type: string
      responses:
        '200':
          description: Successful operation
          content:
            application/json:
              schema:
                $ref: './component/external/supplierdetails/ExternalSupplierDetailsResponse.yaml'
        '404':
          description: Supplier not found
    put:
      tags:
        - Suppliers
      summary: Update a specific supplier
      operationId: updateSupplier
      parameters:
        - name: supplier_key
          in: path
          required: true
          schema:
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: './component/external/supplierdetails/ExternalSupplierDetailsData.yaml'
      responses:
        '200':
          description: The supplier was successfully updated
          content:
            application/json:
              schema:
                $ref: './component/external/supplierdetails/ExternalSupplierDetailsResponse.yaml'
        '400':
          description: Invalid input
        '404':
          description: Supplier not found
  /external/v1/suppliers/{supplier_key}/billing-address:
    get:
      tags:
        - Suppliers
      summary: Get the billing address of a specific supplier
      operationId: loadSupplierBillingAddress
      parameters:
        - name: supplier_key
          in: path
          required: true
          schema:
            type: string
      responses:
        '200':
          description: Successful operation
          content:
            application/json:
              schema:
                $ref: './component/model/Address.yaml'
        '404':
          description: Supplier not found
    put:
      tags:
        - Suppliers
      summary: Update the billing address of a specific supplier
      operationId: updateSupplierBillingAddress
      parameters:
        - name: supplier_key
          in: path
          required: true
          schema:
            type: string
        - name: address_key
          in: path
          required: true
          schema:
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: './component/model/Address.yaml'
      responses:
        '200':
          description: The address was successfully updated
          content:
            application/json:
              schema:
                $ref: '././component/model/Address.yaml'
        '400':
          description: Invalid input
        '404':
          description: Supplier not found

  /external/v1/orders:
    get:
      tags:
        - Orders
      summary: Load all orders
      operationId: loadOrders
      parameters:
        - name: page
          in: query
          description: The index of the page to retrieve
          required: true
          schema:
            type: integer
            format: int32
        - name: size
          in: query
          description: The number of items per page
          required: true
          schema:
            type: integer
            format: int32
        - name: sort
          in: query
          description: The field to sort the orders by. Supported values are TODO
          schema:
            type: string
        - name: direction
          in: query
          description: The direction to sort by. Supported values are 'asc', 'desc'
          schema:
            type: string
        - name: filter
          in: query
          description: The filter to apply on the orders. This can be any attribute of the order.
          schema:
            type: string
      responses:
        '200':
          description: Page of orders
          content:
            application/json:
              schema:
                $ref: './component/external/orderlist/ExternalOrdersPagedResponse.yaml'
        '400':
          description: Invalid input
        '404':
          description: Customer not found
    post:
      tags:
        - Orders
      summary: Create a new order
      operationId: createOrder
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: './component/external/ExternalOrderRequest.yaml'
      responses:
        '202':
          description: The order was successfully accepted
        '400':
          description: Invalid input
        '404':
          description: Customer not found
  /external/v1/orders/{order_key}:
    get:
      summary: Get a specific order
      operationId: loadOrder
      tags:
        - Orders
      parameters:
        - name: order_key
          in: path
          required: true
          schema:
            type: string
      responses:
        '200':
          description: Successful operation
          content:
            application/json:
              schema:
                $ref: './component/external/ExternalOrderResponse.yaml'
        '404':
          description: Customer or Order not found
    put:
      tags:
        - Orders
      summary: Update a specific order (complete update)
      operationId: updateOrder
      parameters:
        - name: order_key
          in: path
          required: true
          schema:
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: './component/external/ExternalOrderRequest.yaml'
      responses:
        '202':
          description: The order update was accepted
        '400':
          description: Invalid input
        '404':
          description: Customer or Order not found
    patch:
      tags:
        - Orders
      summary: Update a specific order (partial update)
      operationId: updateOrderPartial
      parameters:
        - name: order_key
          in: path
          required: true
          schema:
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: './component/external/ExternalOrderRequest.yaml'
      responses:
        '202':
          description: The order partial update was accepted
        '400':
          description: Invalid input
        '404':
          description: Customer or Order not found
  /external/v1/orders/{order_key}/finalize:
    post:
      tags:
        - Orders
      summary: finalize order by Id
      operationId: finalizeOrder
      parameters:
        - name: order_key
          in: path
          required: true
          schema:
            type: string
      responses:
        '202':
          description: Request accepted
        '404':
          description: Customer or Order not found
  /external/v1/orders/{order_key}/cancel:
    post:
      tags:
        - Orders
      summary: cancel order by Id
      operationId: cancelOrder
      parameters:
        - name: order_key
          in: path
          required: true
          schema:
            type: string
      responses:
        '202':
          description: Request accepted
        '404':
          description: Customer or Order not found
  /external/v1/orders/{order_key}/order-items:
    get:
      tags:
        - Orders
      summary: Load all order items of a specific order
      operationId: loadOrderItems
      parameters:
        - name: order_key
          in: path
          required: true
          schema:
            type: string
        - name: page
          in: query
          description: The index of the page to retrieve
          required: true
          schema:
            type: integer
            format: int32
        - name: size
          in: query
          description: The number of items per page
          required: true
          schema:
            type: integer
            format: int32
        - name: sort
          in: query
          description: The field to sort the orders by. Supported values are 'createdAt', 'firstName', 'lastName', 'companyName'
          schema:
            type: string
        - name: direction
          in: query
          description: The direction to sort by. Supported values are 'asc', 'desc'
          schema:
            type: string
        - name: filter
          in: query
          description: The filter to apply on the customers. This can be any attribute of the customer.
          schema:
            type: string
      responses:
        '200':
          description: Page of order items
          content:
            application/json:
              schema:
                $ref: './component/external/orderitemlist/ExternalOrderItemsPagedResponse.yaml'
        '404':
          description: Customer or Order not found
    post:
      tags:
        - Orders
      summary: Add an order item to a specific order
      operationId: addOrderItem
      parameters:
        - name: order_key
          in: path
          required: true
          schema:
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: './component/external/ExternalOrderItemRequest.yaml'
      responses:
        '202':
          description: Order item creation accepted
        '400':
          description: Invalid input
        '404':
          description: Customer or Order not found
  /external/v1/orders/{order_key}/order-items/{order_item_key}:
    get:
      tags:
        - Orders
      summary: Load order item by key from a specific order
      operationId: loadOrderItem
      parameters:
        - name: order_key
          in: path
          required: true
          schema:
            type: string
        - name: order_item_key
          in: path
          required: true
          schema:
            type: string
      responses:
        '200':
          description: successful operation
          content:
            application/json:
              schema:
                $ref: './component/external/ExternalOrderItemResponse.yaml'
        '404':
          description: Customer, Order or Item not found
    put:
      tags:
        - Orders
      summary: Update an existing order item from a specific order
      operationId: updateOrderItem
      parameters:
        - name: order_key
          in: path
          required: true
          schema:
            type: string
        - name: order_item_key
          in: path
          required: true
          schema:
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: './component/external/ExternalOrderItemRequest.yaml'
      responses:
        '202':
          description: Order item update accepted
        '400':
          description: Invalid input
        '404':
          description: Customer, Order or Item not found
    delete:
      tags:
        - Orders
      summary: Delete an order  item from a specific order
      operationId: deleteOrderItem
      parameters:
        - name: order_key
          in: path
          required: true
          schema:
            type: string
        - name: order_item_key
          in: path
          required: true
          schema:
            type: string
      responses:
        '202':
          description: Order item deletion accepted
        '400':
          description: Invalid input
        '404':
          description: Customer, Order or Item not found
  /external/v1/orders/{order_key}/documents/{document_key}/file:
    get:
      summary: Get a specific document file
      operationId: loadDocumentFile
      tags:
        - Documents
      parameters:
        - name: order_key
          in: path
          description: The key of the order
          required: true
          schema:
            type: string
        - name: document_key
          in: path
          description: The key of the document
          required: true
          schema:
            type: string
        - name: format
          in: query
          description: Format of the document
          schema:
            $ref: './component/model/DocumentFormat.yaml'
      responses:
        '200':
          description: Document
          content:
            application/*:
              schema:
                type: string
                format: binary
        '404':
          description: Order or document not found
  /external/v1/settlement-reports:
    get:
      tags:
        - Settlement Reports
      summary: Load all settlement reports by creation date (descending)
      operationId: loadSettlementReports
      parameters:
        - name: page
          in: query
          description: The index of the page to retrieve
          schema:
            type: integer
            format: int32
        - name: size
          in: query
          description: The number of items per page
          schema:
            type: integer
            format: int32
        - name: sort
          in: query
          description: The field to sort the items by. Supported values are TODO
          schema:
            type: string
        - name: direction
          in: query
          description: The direction to sort by. Supported values are 'asc', 'desc'
          schema:
            type: string
        - name: filter
          in: query
          description: The filter to apply. This can be any attribute of the object.
          schema:
            type: string
      responses:
        '200':
          description: Paged response
          content:
            application/json:
              schema:
                $ref: './component/external/settlementreportlist/ExternalSettlementReportsPagedResponse.yaml'
        '400':
          description: Invalid input
    post:
      tags:
        - Settlement Reports
      summary: "Create a settlement report"
      operationId: createSettlementReport
      requestBody:
        content:
          application/json:
            schema:
              $ref: './component/external/settlementreport/ExternalSettlementReport.yaml'
        required: true
      responses:
        '201':
          description: The settlement report was created
        '400':
          description: Invalid input
  /external/v1/settlement-reports/{settlement_report_key}:
    get:
      tags:
        - Settlement Reports
      summary: Load details of settlement report
      operationId: loadSettlementReport
      parameters:
        - name: settlement_report_key
          in: path
          required: true
          description: The key of the settlement report
          schema:
            type: string
      responses:
        '200':
          description: Settlement report details
          content:
            application/json:
              schema:
                $ref: './component/external/settlementreportdetails/ExternalSettlementReportDetailsResponse.yaml'
        '400':
          description: Invalid input
        '404':
          description: Settlement report not found
  /external/v1/settlement-reports/{settlement_report_key}/settlement-report-items:
    get:
      tags:
        - Settlement Reports
      summary: Load all settlement report items by creation date (descending)
      operationId: loadSettlementReportItems
      parameters:
        - name: page
          in: query
          description: The index of the page to retrieve
          schema:
            type: integer
            format: int32
        - name: size
          in: query
          description: The number of items per page
          schema:
            type: integer
            format: int32
        - name: sort
          in: query
          description: The field to sort the items by. Supported values are TODO
          schema:
            type: string
        - name: direction
          in: query
          description: The direction to sort by. Supported values are 'asc', 'desc'
          schema:
            type: string
        - name: filter
          in: query
          description: The filter to apply. This can be any attribute of the object.
          schema:
            type: string
        - name: settlement_report_key
          in: path
          required: true
          description: The key of the settlement report
          schema:
            type: string
      responses:
        '200':
          description: Paged response
          content:
            application/json:
              schema:
                $ref: './component/external/settlementreportdetails/ExternalSettlementReportItemsPagedResponse.yaml'
        '400':
          description: Invalid input
        '404':
          description: Settlement report not found
  /external/v1/settlement-report-files:
    post:
      tags:
        - Settlement Reports
      summary: "Upload a settlement report file (CAMT.053 / MT940)"
      operationId: uploadSettlementReportFile
      requestBody:
        required: true
        content:
          multipart/form-data:
            schema:
              type: object
              properties:
                report:
                  type: string
                  format: binary
              required:
                - report
      responses:
        '200':
          description: The settlement report file was processed successfully
        '400':
          description: Invalid input
  /external/v1/booking-rules:
    get:
      tags:
        - Booking Rules
      summary: Load all booking rules by description
      operationId: loadBookingRules
      parameters:
        - name: page
          in: query
          description: The index of the page to retrieve
          schema:
            type: integer
            format: int32
        - name: size
          in: query
          description: The number of items per page
          schema:
            type: integer
            format: int32
        - name: sort
          in: query
          description: The field to sort the items by. Supported values are TODO
          schema:
            type: string
        - name: direction
          in: query
          description: The direction to sort by. Supported values are 'asc', 'desc'
          schema:
            type: string
        - name: filter
          in: query
          description: The filter to apply on the order items. This can be any attribute of the order items.
          schema:
            type: string
      responses:
        '200':
          description: Page of booking rules
          content:
            application/json:
              schema:
                $ref: './component/external/bookingrulelist/ExternalBookingRulesPagedResponse.yaml'
        '400':
          description: Invalid input
    post:
      tags:
        - Booking Rules
      summary: Create a new booking rule
      operationId: createBookingRule
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: './component/external/bookingruledetails/ExternalBookingRuleDetails.yaml'
      responses:
        '201':
          description: The booking rule was successfully created
          content:
            application/json:
              schema:
                $ref: './component/external/bookingruledetails/ExternalBookingRuleDetails.yaml'
        '400':
          description: Invalid input
  /external/v1/booking-rules/{booking_rule_key}:
    get:
      tags:
        - Booking Rules
      summary: load booking rule by key
      operationId: loadBookingRule
      parameters:
        - name: booking_rule_key
          in: path
          required: true
          schema:
            type: string
      responses:
        '200':
          description: successful operation
          content:
            application/json:
              schema:
                $ref: './component/external/bookingruledetails/ExternalBookingRuleDetails.yaml'
        '404':
          description: Booking rule not found
    put:
      tags:
        - Booking Rules
      summary: Update a specific booking rule
      operationId: updateBookingRule
      parameters:
        - name: booking_rule_key
          in: path
          required: true
          schema:
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: './component/external/bookingruledetails/ExternalBookingRuleDetails.yaml'
      responses:
        '200':
          description: The booking rule was successfully updated
          content:
            application/json:
              schema:
                $ref: './component/external/bookingruledetails/ExternalBookingRuleDetails.yaml'
        '400':
          description: Invalid input
        '404':
          description: Booking rule not found
    delete:
      tags:
        - Booking Rules
      summary: Delete a specific booking rule
      operationId: deleteBookingRule
      parameters:
        - name: booking_rule_key
          in: path
          required: true
          schema:
            type: string
      responses:
        '204':
          description: Booking rule deleted
        '400':
          description: Invalid input
        '404':
          description: Booking rule not found
  /external/v1/payment-transactions:
    get:
      tags:
        - Payment Transactions
      summary: Load all payment transactions
      operationId: loadPaymentTransactions
      parameters:
        - name: page
          in: query
          description: The index of the page to retrieve
          schema:
            type: integer
            format: int32
        - name: size
          in: query
          description: The number of items per page
          schema:
            type: integer
            format: int32
        - name: sort
          in: query
          description: The field to sort the items by. Supported values are TODO
          schema:
            type: string
        - name: direction
          in: query
          description: The direction to sort by. Supported values are 'asc', 'desc'
          schema:
            type: string
        - name: filter
          in: query
          description: The filter to apply on the order items. This can be any attribute of the order items.
          schema:
            type: string
      responses:
        '200':
          description: Page of payment transactions
          content:
            application/json:
              schema:
                $ref: './component/external/paymenttransaction/list/ExternalPaymentTransactionsPagedResponse.yaml'
        '400':
          description: Invalid input
  /external/v1/payment-transactions/{payment_transaction_key}:
    get:
      tags:
        - Payment Transactions
      summary: Load a specific payment transaction
      operationId: loadPaymentTransactionClearing
      parameters:
        - name: payment_transaction_key
          in: path
          required: true
          schema:
            type: string
      responses:
        '200':
          description: Successful operation
          content:
            application/json:
              schema:
                $ref: './component/external/paymenttransaction/details/ExternalPaymentTransactionDetails.yaml'
        '404':
          description: Payment transaction not found
  /external/v1/payment-assignments:
    get:
      tags:
        - Payment Assignments
      summary: Load all payment assignments
      operationId: loadPaymentAssignments
      parameters:
        - name: page
          in: query
          description: The index of the page to retrieve
          schema:
            type: integer
            format: int32
        - name: size
          in: query
          description: The number of items per page
          schema:
            type: integer
            format: int32
        - name: sort
          in: query
          description: The field to sort the items by. Supported values are TODO
          schema:
            type: string
        - name: direction
          in: query
          description: The direction to sort by. Supported values are 'asc', 'desc'
          schema:
            type: string
        - name: filter
          in: query
          description: The filter to apply on the order items. This can be any attribute of the order items.
          schema:
            type: string
      responses:
        '200':
          description: Page of payment assignments
          content:
            application/json:
              schema:
                $ref: './component/external/paymentassignment/list/ExternalPaymentAssignmentsPagedResponse.yaml'
        '400':
          description: Invalid input
    post:
      tags:
        - Payment Assignments
      summary: Create a new payment assignment
      operationId: createPaymentAssignment
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: './component/external/paymentassignment/details/ExternalPaymentAssignmentDetails.yaml'
      responses:
        '201':
          description: The payment assignment was successfully created
          content:
            application/json:
              schema:
                $ref: './component/external/paymentassignment/details/ExternalPaymentAssignmentDetails.yaml'
        '400':
          description: Invalid input
  /external/v1/payment-assignments/{payment_assignment_key}:
    get:
      tags:
        - Payment Assignments
      summary: Load a specific payment assignment
      operationId: loadPaymentAssignment
      parameters:
        - name: payment_assignment_key
          in: path
          required: true
          schema:
            type: string
      responses:
        '200':
          description: Successful operation
          content:
            application/json:
              schema:
                $ref: './component/external/paymentassignment/details/ExternalPaymentAssignmentDetails.yaml'
        '404':
          description: Payment assignment not found
  /external/v1/payment-assignments/{payment_assignment_key}/reverse:
    post:
      tags:
        - Payment Assignments
      summary: Reverse a specific payment assignment
      operationId: reversePaymentAssignment
      parameters:
        - name: payment_assignment_key
          in: path
          required: true
          schema:
            type: string
      responses:
        '202':
          description: Request accepted
        '404':
          description: Payment assignment not found
  /external/v1/booking-accounts:
    get:
      tags:
        - Booking Accounts
      summary: Load all booking accounts by account number (asc)
      operationId: loadBookingAccounts
      parameters:
        - name: page
          in: query
          description: The index of the page to retrieve
          schema:
            type: integer
            format: int32
        - name: size
          in: query
          description: The number of items per page
          schema:
            type: integer
            format: int32
        - name: sort
          in: query
          description: The field to sort the items by. Supported values are TODO
          schema:
            type: string
        - name: direction
          in: query
          description: The direction to sort by. Supported values are 'asc', 'desc'
          schema:
            type: string
        - name: filter
          in: query
          description: The filter to apply on. This can be any attribute of the booking account.
          schema:
            type: string
      responses:
        '200':
          description: Page of booking accounts
          content:
            application/json:
              schema:
                $ref: './component/external/bookingaccountlist/ExternalBookingAccountsPagedResponse.yaml'
        '400':
          description: Invalid input
    post:
      tags:
        - Booking Accounts
      summary: Create a new booking account
      operationId: createBookingAccount
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: './component/external/bookingaccountdetails/ExternalBookingAccountDetails.yaml'
      responses:
        '201':
          description: The booking account was successfully created
          content:
            application/json:
              schema:
                $ref: './component/external/bookingaccountdetails/ExternalBookingAccountDetails.yaml'
        '400':
          description: Invalid input
  /external/v1/booking-accounts/{booking_account_key}:
    get:
      tags:
        - Booking Accounts
      summary: Load details of a specific booking account
      operationId: loadBookingAccount
      parameters:
        - name: booking_account_key
          in: path
          required: true
          description: The key of the booking account
          schema:
            type: string
      responses:
        '200':
          description: Details of the booking account
          content:
            application/json:
              schema:
                $ref: './component/external/bookingaccountdetails/ExternalBookingAccountDetails.yaml'
        '400':
          description: Invalid input
    put:
      tags:
        - Booking Accounts
      summary: Update a specific booking account
      operationId: updateBookingAccount
      parameters:
        - name: booking_account_key
          in: path
          required: true
          schema:
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: './component/external/bookingaccountdetails/ExternalBookingAccountDetails.yaml'
      responses:
        '200':
          description: The booking account was successfully updated
          content:
            application/json:
              schema:
                $ref: './component/external/bookingaccountdetails/ExternalBookingAccountDetails.yaml'
        '400':
          description: Invalid input
        '404':
          description: Booking account not found
  /external/v1/currency-exchange-rates:
    post:
      tags:
        - Currency Exchange Rates
      summary: Create a new currency exchange rate
      operationId: createCurrencyExchangeRate
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: './component/external/currencyexchangeratedetails/ExternalCurrencyExchangeRate.yaml'
      responses:
        '201':
          description: The currency exchange rate was successfully created
          content:
            application/json:
              schema:
                $ref: './component/external/currencyexchangeratedetails/ExternalCurrencyExchangeRate.yaml'
        '400':
          description: Invalid input
  /external/v1/posting-records:
    get:
      tags:
        - Posting Records
      summary: Load all posting records by creation date (descending)
      operationId: loadPostingRecords
      parameters:
        - name: page
          in: query
          description: The index of the page to retrieve
          schema:
            type: integer
            format: int32
        - name: size
          in: query
          description: The number of items per page
          schema:
            type: integer
            format: int32
        - name: sort
          in: query
          description: The field to sort the items by. Supported values are TODO
          schema:
            type: string
        - name: direction
          in: query
          description: The direction to sort by. Supported values are 'asc', 'desc'
          schema:
            type: string
        - name: filter
          in: query
          description: The filter to apply on the order items. This can be any attribute of the order items.
          schema:
            type: string
      responses:
        '200':
          description: Page of posting records
          content:
            application/json:
              schema:
                $ref: './component/external/postingrecordlist/ExternalPostingRecordsPagedResponse.yaml'
        '400':
          description: Invalid input
  /external/v1/posting-records/{posting_record_key}:
    get:
      tags:
        - Posting Records
      summary: Load a specific posting record
      operationId: loadPostingRecord
      parameters:
        - name: posting_record_key
          in: path
          required: true
          schema:
            type: string
      responses:
        '200':
          description: Successful operation
          content:
            application/json:
              schema:
                $ref: './component/external/postingrecorddetails/ExternalPostingRecordDetails.yaml'
        '404':
          description: Posting record not found
  /external/v1/posting-records/{posting_record_key}/entries:
    get:
      tags:
        - Posting Records
      summary: Load paged entries of a specific posting record with pagination
      operationId: loadPostingRecordEntries
      parameters:
        - name: posting_record_key
          in: path
          required: true
          schema:
            type: string
        - name: page
          in: query
          description: The index of the page to retrieve
          schema:
            type: integer
            format: int32
        - name: size
          in: query
          description: The number of items per page
          schema:
            type: integer
            format: int32
        - name: sort
          in: query
          description: The field to sort the items by. Supported values are TODO
          schema:
            type: string
        - name: direction
          in: query
          description: The direction to sort by. Supported values are 'asc', 'desc'
          schema:
            type: string
        - name: filter
          in: query
          description: The filter to apply on the order items. This can be any attribute of the order items.
          schema:
            type: string
      responses:
        '200':
          description: A list of posting record entries
          content:
            application/json:
              schema:
                $ref: './component/external/postingrecorddetails/ExternalPostingRecordEntriesPagedResponse.yaml'
        '400':
          description: Invalid input
        '404':
          description: Order not found
  /external/v1/business-segments:
    get:
      tags:
        - Business Segments
      summary: Load all business segments
      operationId: loadBusinessSegments
      parameters:
        - name: page
          in: query
          description: The index of the page to retrieve
          schema:
            type: integer
            format: int32
        - name: size
          in: query
          description: The number of items per page
          schema:
            type: integer
            format: int32
        - name: sort
          in: query
          description: The field to sort the items by
          schema:
            type: string
        - name: direction
          in: query
          description: The direction to sort by. Supported values are 'asc', 'desc'
          schema:
            type: string
        - name: filter
          in: query
          description: The filter to apply on the business segments. This can be any attribute of the business segment.
          schema:
            type: string
      responses:
        '200':
          description: Page of business segments
          content:
            application/json:
              schema:
                $ref: './component/external/businesssegmentlist/ExternalBusinessSegmentsPagedResponse.yaml'
        '400':
          description: Invalid input
    post:
      tags:
        - Business Segments
      summary: Create a new business segment
      operationId: createBusinessSegment
      parameters:
        - name: key
          in: path
          required: true
          schema:
            type: string
        - name: invoiceNumberPattern
          in: query
          required: true
          schema:
            type: string
            description: The invoice number pattern
        - name: vatId
          in: query
          required: true
          schema:
            type: string
            description: The VAT ID
        - name: originatorAddressKey
          in: query
          required: true
          schema:
            type: string
            description: The key of the originator address
        - name: version
          in: query
          required: true
          schema:
            type: integer
            format: int64
      requestBody:
        required: true
        content:
          multipart/form-data:
            schema:
              type: object
              properties:
                logo:
                  type: string
                  format: binary
              required:
                - logo
      responses:
        '201':
          description: The business segment was successfully created
          content:
            application/json:
              schema:
                $ref: './component/external/businesssegmentdetails/ExternalBusinessSegmentDetails.yaml'
        '400':
          description: Invalid input
  /external/v1/business-segments/{business_segment_key}:
    get:
      tags:
        - Business Segments
      summary: Load a specific business segment
      operationId: loadBusinessSegment
      parameters:
        - name: business_segment_key
          in: path
          required: true
          schema:
            type: string
      responses:
        '200':
          description: Successful operation
          content:
            application/json:
              schema:
                $ref: './component/external/businesssegmentdetails/ExternalBusinessSegmentDetails.yaml'
        '404':
          description: Business segment not found
    put:
      tags:
        - Business Segments
      summary: Update a specific business segment
      operationId: updateBusinessSegment
      parameters:
        - name: business_segment_key
          in: path
          required: true
          schema:
            type: string
        - name: invoiceNumberPattern
          in: query
          required: true
          schema:
            type: string
            description: The invoice number pattern
        - name: vatId
          in: query
          required: true
          schema:
            type: string
            description: The VAT ID
        - name: originatorAddressKey
          in: query
          required: true
          schema:
            type: string
            description: The key of the originator address
        - name: version
          in: query
          required: true
          schema:
            type: integer
            format: int64
      requestBody:
        required: true
        content:
          multipart/form-data:
            schema:
              type: object
              properties:
                logo:
                  type: string
                  format: binary
      responses:
        '200':
          description: The business segment was successfully updated
          content:
            application/json:
              schema:
                $ref: './component/external/businesssegmentdetails/ExternalBusinessSegmentDetails.yaml'
        '400':
          description: Invalid input
        '404':
          description: Business segment not found
    delete:
      tags:
        - Business Segments
      summary: Delete a specific business segment
      operationId: deleteBusinessSegment
      parameters:
        - name: business_segment_key
          in: path
          required: true
          schema:
            type: string
      responses:
        '204':
          description: Business segment deleted
        '400':
          description: Invalid input
        '404':
          description: Business segment not found
  /external/v1/approvals:
    get:
      tags:
        - Approvals
      summary: Load all approvals by creation date (descending)
      operationId: loadApprovals
      parameters:
        - name: page
          in: query
          description: The index of the page to retrieve
          schema:
            type: integer
            format: int32
        - name: size
          in: query
          description: The number of items per page
          schema:
            type: integer
            format: int32
        - name: sort
          in: query
          description: The field to sort the items by. Supported values are TODO
          schema:
            type: string
        - name: direction
          in: query
          description: The direction to sort by. Supported values are 'asc', 'desc'
          schema:
            type: string
        - name: filter
          in: query
          description: The filter to apply on the approvals. This can be any attribute of the approvals.
          schema:
            type: string
      responses:
        '200':
          description: Page of approvals
          content:
            application/json:
              schema:
                $ref: './component/external/approvallist/ExternalApprovalsPagedResponse.yaml'
        '400':
          description: Invalid input
  /external/v1/approvals/{approval_key}:
    get:
      tags:
        - Approvals
      summary: Load a specific approval
      operationId: loadApproval
      parameters:
        - name: approval_key
          in: path
          required: true
          schema:
            type: string
      responses:
        '200':
          description: Successful operation
          content:
            application/json:
              schema:
                $ref: './component/external/approvaldetails/ExternalApprovalDetails.yaml'
        '404':
          description: Approval not found
  /external/v1/approvals/{approval_key}/approve:
    post:
      tags:
        - Approvals
      summary: Approve a specific approval
      operationId: approveApproval
      parameters:
        - name: approval_key
          in: path
          required: true
          schema:
            type: string
        - name: body
          in: query
          description: The body of the request
          required: true
          schema:
            type: object
            properties:
              comment:
                type: string
              version:
                type: integer
                format: int64
            required:
              - version
      responses:
        '200':
          description: Successful operation
        '404':
          description: Approval not found
  /external/v1/approvals/{approval_key}/reject:
    post:
      tags:
        - Approvals
      summary: Reject a specific approval
      operationId: rejectApproval
      parameters:
        - name: approval_key
          in: path
          required: true
          schema:
            type: string
        - name: body
          in: query
          description: The body of the request
          required: true
          schema:
            type: object
            properties:
              comment:
                type: string
              version:
                type: integer
                format: int64
            required:
              - version
      responses:
        '200':
          description: Successful operation
        '404':
          description: Approval not found
  /external/v1/reports:
    get:
      tags:
        - Reports
      summary: Load all reports by creation date (descending)
      operationId: loadReports
      parameters:
        - name: page
          in: query
          description: The index of the page to retrieve
          schema:
            type: integer
            format: int32
        - name: size
          in: query
          description: The number of items per page
          schema:
            type: integer
            format: int32
        - name: sort
          in: query
          description: The field to sort the items by. Supported values are TODO
          schema:
            type: string
        - name: direction
          in: query
          description: The direction to sort by. Supported values are 'asc', 'desc'
          schema:
            type: string
        - name: filter
          in: query
          description: The filter to apply on the reports. This can be any attribute of the reports.
          schema:
            type: string
      responses:
        '200':
          description: Page of reports
          content:
            application/json:
              schema:
                $ref: './component/external/reportlist/ExternalReportsPagedResponse.yaml'
        '400':
          description: Invalid input
  /external/v1/reports/{report_key}:
    get:
      tags:
        - Reports
      summary: Load details of a specific report
      operationId: loadReport
      parameters:
        - name: report_key
          in: path
          required: true
          schema:
            type: string
      responses:
        '200':
          description: Successful operation
          content:
            application/json:
              schema:
                $ref: './component/external/reportdetails/ExternalReport.yaml'
        '404':
          description: Report not found
  /external/v1/reports/{report_key}/file:
    get:
      tags:
        - Reports
      summary: Get a specific report file
      operationId: loadReportFile
      parameters:
        - name: report_key
          in: path
          description: The key of the report
          required: true
          schema:
            type: string
      responses:
        '200':
          description: Report file
          content:
            application/*:
              schema:
                type: string
                format: binary
        '404':
          description: Order, document or file not found
  /external/v1/balance-cases:
    get:
      tags:
        - Balance Cases
      summary: Load all balance cases by creation date (descending)
      operationId: loadBalanceCases
      parameters:
        - name: page
          in: query
          description: The index of the page to retrieve
          schema:
            type: integer
            format: int32
        - name: size
          in: query
          description: The number of items per page
          schema:
            type: integer
            format: int32
        - name: sort
          in: query
          description: The field to sort the items by. Supported values are TODO
          schema:
            type: string
        - name: direction
          in: query
          description: The direction to sort by. Supported values are 'asc', 'desc'
          schema:
            type: string
        - name: filter
          in: query
          description: The filter to apply. This can be any attribute of the object.
          schema:
            type: string
      responses:
        '200':
          description: Paged response
          content:
            application/json:
              schema:
                $ref: './component/external/balancecaselist/ExternalBalanceCasesPagedResponse.yaml'
        '400':
          description: Invalid input
  /external/v1/balance-cases/{balance_case_key}:
    get:
      tags:
        - Balance Cases
      summary: Load details of balance case
      operationId: loadBalanceCase
      parameters:
        - name: balance_case_key
          in: path
          required: true
          description: The key of the balance case
          schema:
            type: string
      responses:
        '200':
          description: Balance case details
          content:
            application/json:
              schema:
                $ref: './component/external/balancecasedetails/ExternalBalanceCaseDetailsResponse.yaml'
        '400':
          description: Invalid input
        '404':
          description: Balance case not found
  /external/v1/balance-cases/{balance_case_key}/balance-case-items:
    get:
      tags:
        - Balance Cases
      summary: Load all balance case items by creation date (descending)
      operationId: loadBalanceCaseItems
      parameters:
        - name: page
          in: query
          description: The index of the page to retrieve
          schema:
            type: integer
            format: int32
        - name: size
          in: query
          description: The number of items per page
          schema:
            type: integer
            format: int32
        - name: sort
          in: query
          description: The field to sort the items by. Supported values are TODO
          schema:
            type: string
        - name: direction
          in: query
          description: The direction to sort by. Supported values are 'asc', 'desc'
          schema:
            type: string
        - name: filter
          in: query
          description: The filter to apply. This can be any attribute of the object.
          schema:
            type: string
        - name: balance_case_key
          in: path
          required: true
          description: The key of the balance case
          schema:
            type: string
      responses:
        '200':
          description: Paged response
          content:
            application/json:
              schema:
                $ref: './component/external/balancecasedetails/ExternalBalanceCaseItemsPagedResponse.yaml'
        '400':
          description: Invalid input
        '404':
          description: Balance case not found
  /external/v1/payment-accounts:
    get:
      tags:
        - Payment Accounts
      summary: Load all payment accounts by account id (asc)
      operationId: loadPaymentAccounts
      parameters:
        - name: page
          in: query
          description: The index of the page to retrieve
          schema:
            type: integer
            format: int32
        - name: size
          in: query
          description: The number of items per page
          schema:
            type: integer
            format: int32
        - name: sort
          in: query
          description: The field to sort the items by. Supported values are TODO
          schema:
            type: string
        - name: direction
          in: query
          description: The direction to sort by. Supported values are 'asc', 'desc'
          schema:
            type: string
        - name: filter
          in: query
          description: The filter to apply on. This can be any attribute of the object.
          schema:
            type: string
      responses:
        '200':
          description: Page of payment accounts
          content:
            application/json:
              schema:
                $ref: './component/external/paymentaccountlist/ExternalPaymentAccountsPagedResponse.yaml'
        '400':
          description: Invalid input
    post:
      tags:
        - Payment Accounts
      summary: Create a new payment account
      operationId: createPaymentAccount
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: './component/external/paymentaccountdetails/ExternalPaymentAccountDetails.yaml'
      responses:
        '201':
          description: The payment account was successfully created
          content:
            application/json:
              schema:
                $ref: './component/external/paymentaccountdetails/ExternalPaymentAccountDetails.yaml'
        '400':
          description: Invalid input
  /external/v1/payment-accounts/{payment_account_key}:
    get:
      tags:
        - Payment Accounts
      summary: Load payment account by key
      operationId: loadPaymentAccount
      parameters:
        - name: payment_account_key
          in: path
          required: true
          schema:
            type: string
      responses:
        '200':
          description: successful operation
          content:
            application/json:
              schema:
                $ref: './component/external/paymentaccountdetails/ExternalPaymentAccountDetails.yaml'
        '404':
          description: Payment account not found
    put:
      tags:
        - Payment Accounts
      summary: Update a specific payment account
      operationId: updatePaymentAccount
      parameters:
        - name: payment_account_key
          in: path
          required: true
          schema:
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: './component/external/paymentaccountdetails/ExternalPaymentAccountDetails.yaml'
      responses:
        '200':
          description: The payment account was successfully updated
          content:
            application/json:
              schema:
                $ref: './component/external/paymentaccountdetails/ExternalPaymentAccountDetails.yaml'
        '400':
          description: Invalid input
        '404':
          description: Payment account not found
    delete:
      tags:
        - Payment Accounts
      summary: Delete a specific payment account
      operationId: deletePaymentAccount
      parameters:
        - name: payment_account_key
          in: path
          required: true
          schema:
            type: string
      responses:
        '204':
          description: The payment account was successfully deleted
        '404':
          description: Payment account not found
  /external/v1/payment-categorization-rules:
    get:
      tags:
        - Payment Categorization Rules
      summary: Load all payment categorization rules by name (asc)
      operationId: loadPaymentCategorizationRules
      parameters:
        - name: page
          in: query
          description: The index of the page to retrieve
          schema:
            type: integer
            format: int32
        - name: size
          in: query
          description: The number of items per page
          schema:
            type: integer
            format: int32
        - name: sort
          in: query
          description: The field to sort the items by. Supported values are TODO
          schema:
            type: string
        - name: direction
          in: query
          description: The direction to sort by. Supported values are 'asc', 'desc'
          schema:
            type: string
        - name: filter
          in: query
          description: The filter to apply on. This can be any attribute of the object.
          schema:
            type: string
      responses:
        '200':
          description: Page of payment categorization rules
          content:
            application/json:
              schema:
                $ref: './component/external/paymentcategorizationrulelist/ExternalPaymentCategorizationRulesPagedResponse.yaml'
        '400':
          description: Invalid input
    post:
      tags:
        - Payment Categorization Rules
      summary: Create a new payment categorization rule
      operationId: createPaymentCategorizationRule
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: './component/external/paymentcategorizationruledetails/ExternalPaymentCategorizationRuleDetails.yaml'
      responses:
        '201':
          description: The payment categorization rule was successfully created
          content:
            application/json:
              schema:
                $ref: './component/external/paymentcategorizationruledetails/ExternalPaymentCategorizationRuleDetails.yaml'
        '400':
          description: Invalid input
  /external/v1/payment-categorization-rules/{payment_categorization_key}:
    get:
      tags:
        - Payment Categorization Rules
      summary: Load payment categorization rule by key
      operationId: loadPaymentCategorizationRule
      parameters:
        - name: payment_categorization_key
          in: path
          required: true
          schema:
            type: string
          description: The key of the payment categorization rule
      responses:
        '200':
          description: successful operation
          content:
            application/json:
              schema:
                $ref: './component/external/paymentcategorizationruledetails/ExternalPaymentCategorizationRuleDetails.yaml'
        '404':
          description: Payment categorization rule not found
    put:
      tags:
        - Payment Categorization Rules
      summary: Update a specific payment categorization rule
      operationId: updatePaymentCategorizationRule
      parameters:
        - name: payment_categorization_key
          in: path
          required: true
          schema:
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: './component/external/paymentcategorizationruledetails/ExternalPaymentCategorizationRuleDetails.yaml'
      responses:
        '200':
          description: The payment categorization rule was successfully updated
          content:
            application/json:
              schema:
                $ref: './component/external/paymentcategorizationruledetails/ExternalPaymentCategorizationRuleDetails.yaml'
        '400':
          description: Invalid input
        '404':
          description: Payment categorization rule not found
    delete:
      tags:
        - Payment Categorization Rules
      summary: Delete a specific payment categorization rule
      operationId: deletePaymentCategorizationRule
      parameters:
        - name: payment_categorization_key
          in: path
          required: true
          schema:
            type: string
      responses:
        '204':
          description: The payment categorization rule was successfully deleted
        '404':
          description: Payment categorization rule not found
  /external/v1/subscriptions:
    get:
      tags:
        - Subscriptions
      summary: Load all subscriptions
      operationId: loadSubscriptions
      parameters:
        - name: page
          in: query
          description: The index of the page to retrieve
          schema:
            type: integer
            format: int32
        - name: size
          in: query
          description: The number of items per page
          schema:
            type: integer
            format: int32
        - name: sort
          in: query
          description: The field to sort the subscriptions by. Supported values are 'createdAt', 'name', 'customerKey'
          schema:
            type: string
        - name: direction
          in: query
          description: The direction to sort by. Supported values are 'asc', 'desc'
          schema:
            type: string
        - name: filter
          in: query
          description: The filter to apply on the subscriptions. This can be any attribute of the subscription.
          schema:
            type: string
      responses:
        '200':
          description: Page of subscriptions
          content:
            application/json:
              schema:
                $ref: './component/external/subscriptionlist/ExternalSubscriptionsPagedResponse.yaml'
        '400':
          description: Invalid input
    post:
      tags:
        - Subscriptions
      summary: Create a new subscription
      operationId: createSubscription
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: './component/external/subscriptiondetails/ExternalSubscriptionDetailsData.yaml'
      responses:
        '201':
          description: The subscription was successfully created
          content:
            application/json:
              schema:
                $ref: './component/external/subscriptiondetails/ExternalSubscriptionDetailsResponse.yaml'
        '400':
          description: Invalid input
  /external/v1/subscriptions/{subscription_key}:
    get:
      tags:
        - Subscriptions
      operationId: loadSubscription
      summary: Get a specific subscription
      parameters:
        - name: subscription_key
          in: path
          required: true
          schema:
            type: string
      responses:
        '200':
          description: Successful operation
          content:
            application/json:
              schema:
                $ref: './component/external/subscriptiondetails/ExternalSubscriptionDetailsResponse.yaml'
        '404':
          description: Subscription not found
    put:
      tags:
        - Subscriptions
      summary: Update a specific subscription
      operationId: updateSubscription
      parameters:
        - name: subscription_key
          in: path
          required: true
          schema:
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: './component/external/subscriptiondetails/ExternalSubscriptionDetailsData.yaml'
      responses:
        '200':
          description: The subscription was successfully updated
          content:
            application/json:
              schema:
                $ref: './component/external/subscriptiondetails/ExternalSubscriptionDetailsResponse.yaml'
        '400':
          description: Invalid input
        '404':
          description: Subscription not found
    delete:
      tags:
        - Subscriptions
      summary: Delete a specific subscription
      operationId: deleteSubscription
      parameters:
        - name: subscription_key
          in: path
          required: true
          schema:
            type: string
      responses:
        '204':
          description: Subscription deleted successfully
        '404':
          description: Subscription not found
  /external/v1/subscriptions/{subscription_key}/subscription-items:
    get:
      tags:
        - Subscriptions
      summary: Load all subscription items of a specific subscription
      operationId: loadSubscriptionItems
      parameters:
        - name: subscription_key
          in: path
          required: true
          schema:
            type: string
        - name: page
          in: query
          description: The index of the page to retrieve
          required: true
          schema:
            type: integer
            format: int32
        - name: size
          in: query
          description: The number of items per page
          required: true
          schema:
            type: integer
            format: int32
        - name: sort
          in: query
          description: The field to sort the subscription items by. Supported values are 'createdAt', 'name', 'articleNumber'
          schema:
            type: string
        - name: direction
          in: query
          description: The direction to sort by. Supported values are 'asc', 'desc'
          schema:
            type: string
        - name: filter
          in: query
          description: The filter to apply on the subscription items. This can be any attribute of the subscription item.
          schema:
            type: string
      responses:
        '200':
          description: Page of subscription items
          content:
            application/json:
              schema:
                $ref: './component/external/subscriptionitemlist/ExternalSubscriptionItemsPagedResponse.yaml'
        '404':
          description: Subscription not found
    post:
      tags:
        - Subscriptions
      summary: Add a subscription item to a specific subscription
      operationId: addSubscriptionItem
      parameters:
        - name: subscription_key
          in: path
          required: true
          schema:
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: './component/external/ExternalSubscriptionItemRequest.yaml'
      responses:
        '202':
          description: Subscription item creation accepted
        '400':
          description: Invalid input
        '404':
          description: Subscription not found
  /external/v1/subscriptions/{subscription_key}/subscription-items/{subscription_item_key}:
    get:
      tags:
        - Subscriptions
      summary: Load subscription item by key from a specific subscription
      operationId: loadSubscriptionItem
      parameters:
        - name: subscription_key
          in: path
          required: true
          schema:
            type: string
        - name: subscription_item_key
          in: path
          required: true
          schema:
            type: string
      responses:
        '200':
          description: successful operation
          content:
            application/json:
              schema:
                $ref: './component/external/ExternalSubscriptionItemResponse.yaml'
        '404':
          description: Subscription or Item not found
    put:
      tags:
        - Subscriptions
      summary: Update an existing subscription item from a specific subscription
      operationId: updateSubscriptionItem
      parameters:
        - name: subscription_key
          in: path
          required: true
          schema:
            type: string
        - name: subscription_item_key
          in: path
          required: true
          schema:
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: './component/external/ExternalSubscriptionItemRequest.yaml'
      responses:
        '202':
          description: Subscription item update accepted
        '400':
          description: Invalid input
        '404':
          description: Subscription or Item not found
    delete:
      tags:
        - Subscriptions
      summary: Delete a subscription item from a specific subscription
      operationId: deleteSubscriptionItem
      parameters:
        - name: subscription_key
          in: path
          required: true
          schema:
            type: string
        - name: subscription_item_key
          in: path
          required: true
          schema:
            type: string
      responses:
        '202':
          description: Subscription item deletion accepted
        '400':
          description: Invalid input
        '404':
          description: Subscription or Item not found
