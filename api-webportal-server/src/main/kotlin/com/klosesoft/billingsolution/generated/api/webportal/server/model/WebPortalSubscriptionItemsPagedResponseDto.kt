package com.klosesoft.billingsolution.generated.api.webportal.server.model

import java.util.Objects
import com.fasterxml.jackson.annotation.JsonProperty
import com.klosesoft.billingsolution.generated.api.webportal.server.model.PageDataDto
import com.klosesoft.billingsolution.generated.api.webportal.server.model.WebPortalSubscriptionItemDto
import jakarta.validation.constraints.DecimalMax
import jakarta.validation.constraints.DecimalMin
import jakarta.validation.constraints.Email
import jakarta.validation.constraints.Max
import jakarta.validation.constraints.Min
import jakarta.validation.constraints.NotNull
import jakarta.validation.constraints.Pattern
import jakarta.validation.constraints.Size
import jakarta.validation.Valid

/**
 * 
 * @param pageData 
 * @param items 
 */
data class WebPortalSubscriptionItemsPagedResponseDto(

    @field:Valid
    @get:JsonProperty("pageData", required = true) val pageData: PageDataDto,

    @field:Valid
    @get:JsonProperty("items", required = true) val items: kotlin.collections.List<WebPortalSubscriptionItemDto>
    ) {

}

