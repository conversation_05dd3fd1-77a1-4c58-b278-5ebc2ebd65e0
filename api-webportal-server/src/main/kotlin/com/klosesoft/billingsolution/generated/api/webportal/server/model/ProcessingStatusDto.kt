package com.klosesoft.billingsolution.generated.api.webportal.server.model

import java.util.Objects
import com.fasterxml.jackson.annotation.JsonValue
import com.fasterxml.jackson.annotation.JsonCreator
import com.fasterxml.jackson.annotation.JsonProperty
import jakarta.validation.constraints.DecimalMax
import jakarta.validation.constraints.DecimalMin
import jakarta.validation.constraints.Email
import jakarta.validation.constraints.Max
import jakarta.validation.constraints.Min
import jakarta.validation.constraints.NotNull
import jakarta.validation.constraints.Pattern
import jakarta.validation.constraints.Size
import jakarta.validation.Valid

/**
* The processing status of the order or subscription
* Values: INIT,ERROR,PROCESSED,ORDER_CREATED,CREDIT_NOTE_CREATED,CREDIT_NOTE_POSTING_RECORD_CREATED,CREDIT_NOTE_PAYOUT_CREATED,CREDIT_NOTE_PAYOUT_POSTING_RECORD_CREATED,DEPOSIT_INVOICE_CREATED,DEPOSIT_PAYMENT_POSTING_RECORD_CREATED,DEPOSIT_POSTING_RECORD_CREATED,DEPOSIT_PAYMENT_DEBITED,FINAL_INVOICE_CREATED,FINAL_INVOICE_CREDIT_ONLY_CREATED,FINAL_INVOICE_DEBIT_ONLY_CREATED,FINAL_INVOICE_PAYMENT_POSTING_RECORD_CREATED,FINAL_INVOICE_POSTING_RECORD_CREATED,FINAL_INVOICE_PAYMENT_DEBITED,FINAL_SELFBILLING_INVOICE_CREATED,FINAL_SELFBILLING_INVOICE_POSTING_RECORD_CREATED,FINAL_SELFBILLING_INVOICE_REVERSED_CREATED,FINAL_SELFBILLING_INVOICE_REVERSED_POSTING_RECORD_CREATED,FINAL_COMMISSION_INVOICE_CREATED,FINAL_COMMISSION_INVOICE_POSTING_RECORD_CREATED,FINAL_COMMISSION_INVOICE_REVERSED_CREATED,FINAL_COMMISSION_INVOICE_REVERSED_POSTING_RECORD_CREATED,ORDER_UPDATED,PAYMENT_ASSIGNMENT_CREATED,PAYMENT_ASSIGNMENT_POSTING_RECORD_CREATED,CANCELLATION_APPROVAL_CREATED,CANCELLATION_APPROVED,CANCELLATION_REJECTED,CUSTOMER_EMAIL_SENT,SUBSCRIPTION_CREATED,SUBSCRIPTION_ACTIVATED,SUBSCRIPTION_BILLING_PROCESSED,SUBSCRIPTION_INVOICE_CREATED,SUBSCRIPTION_INVOICE_SENT,SUBSCRIPTION_PAUSED,SUBSCRIPTION_RESUMED,SUBSCRIPTION_CANCELLED,SUBSCRIPTION_EXPIRED,SUBSCRIPTION_UPDATED
*/
enum class ProcessingStatusDto(@get:JsonValue val value: kotlin.String) {

    INIT("INIT"),
    ERROR("ERROR"),
    PROCESSED("PROCESSED"),
    ORDER_CREATED("ORDER_CREATED"),
    CREDIT_NOTE_CREATED("CREDIT_NOTE_CREATED"),
    CREDIT_NOTE_POSTING_RECORD_CREATED("CREDIT_NOTE_POSTING_RECORD_CREATED"),
    CREDIT_NOTE_PAYOUT_CREATED("CREDIT_NOTE_PAYOUT_CREATED"),
    CREDIT_NOTE_PAYOUT_POSTING_RECORD_CREATED("CREDIT_NOTE_PAYOUT_POSTING_RECORD_CREATED"),
    DEPOSIT_INVOICE_CREATED("DEPOSIT_INVOICE_CREATED"),
    DEPOSIT_PAYMENT_POSTING_RECORD_CREATED("DEPOSIT_PAYMENT_POSTING_RECORD_CREATED"),
    DEPOSIT_POSTING_RECORD_CREATED("DEPOSIT_POSTING_RECORD_CREATED"),
    DEPOSIT_PAYMENT_DEBITED("DEPOSIT_PAYMENT_DEBITED"),
    FINAL_INVOICE_CREATED("FINAL_INVOICE_CREATED"),
    FINAL_INVOICE_CREDIT_ONLY_CREATED("FINAL_INVOICE_CREDIT_ONLY_CREATED"),
    FINAL_INVOICE_DEBIT_ONLY_CREATED("FINAL_INVOICE_DEBIT_ONLY_CREATED"),
    FINAL_INVOICE_PAYMENT_POSTING_RECORD_CREATED("FINAL_INVOICE_PAYMENT_POSTING_RECORD_CREATED"),
    FINAL_INVOICE_POSTING_RECORD_CREATED("FINAL_INVOICE_POSTING_RECORD_CREATED"),
    FINAL_INVOICE_PAYMENT_DEBITED("FINAL_INVOICE_PAYMENT_DEBITED"),
    FINAL_SELFBILLING_INVOICE_CREATED("FINAL_SELFBILLING_INVOICE_CREATED"),
    FINAL_SELFBILLING_INVOICE_POSTING_RECORD_CREATED("FINAL_SELFBILLING_INVOICE_POSTING_RECORD_CREATED"),
    FINAL_SELFBILLING_INVOICE_REVERSED_CREATED("FINAL_SELFBILLING_INVOICE_REVERSED_CREATED"),
    FINAL_SELFBILLING_INVOICE_REVERSED_POSTING_RECORD_CREATED("FINAL_SELFBILLING_INVOICE_REVERSED_POSTING_RECORD_CREATED"),
    FINAL_COMMISSION_INVOICE_CREATED("FINAL_COMMISSION_INVOICE_CREATED"),
    FINAL_COMMISSION_INVOICE_POSTING_RECORD_CREATED("FINAL_COMMISSION_INVOICE_POSTING_RECORD_CREATED"),
    FINAL_COMMISSION_INVOICE_REVERSED_CREATED("FINAL_COMMISSION_INVOICE_REVERSED_CREATED"),
    FINAL_COMMISSION_INVOICE_REVERSED_POSTING_RECORD_CREATED("FINAL_COMMISSION_INVOICE_REVERSED_POSTING_RECORD_CREATED"),
    ORDER_UPDATED("ORDER_UPDATED"),
    PAYMENT_ASSIGNMENT_CREATED("PAYMENT_ASSIGNMENT_CREATED"),
    PAYMENT_ASSIGNMENT_POSTING_RECORD_CREATED("PAYMENT_ASSIGNMENT_POSTING_RECORD_CREATED"),
    CANCELLATION_APPROVAL_CREATED("CANCELLATION_APPROVAL_CREATED"),
    CANCELLATION_APPROVED("CANCELLATION_APPROVED"),
    CANCELLATION_REJECTED("CANCELLATION_REJECTED"),
    CUSTOMER_EMAIL_SENT("CUSTOMER_EMAIL_SENT"),
    SUBSCRIPTION_CREATED("SUBSCRIPTION_CREATED"),
    SUBSCRIPTION_ACTIVATED("SUBSCRIPTION_ACTIVATED"),
    SUBSCRIPTION_BILLING_PROCESSED("SUBSCRIPTION_BILLING_PROCESSED"),
    SUBSCRIPTION_INVOICE_CREATED("SUBSCRIPTION_INVOICE_CREATED"),
    SUBSCRIPTION_INVOICE_SENT("SUBSCRIPTION_INVOICE_SENT"),
    SUBSCRIPTION_PAUSED("SUBSCRIPTION_PAUSED"),
    SUBSCRIPTION_RESUMED("SUBSCRIPTION_RESUMED"),
    SUBSCRIPTION_CANCELLED("SUBSCRIPTION_CANCELLED"),
    SUBSCRIPTION_EXPIRED("SUBSCRIPTION_EXPIRED"),
    SUBSCRIPTION_UPDATED("SUBSCRIPTION_UPDATED");

    companion object {
        @JvmStatic
        @JsonCreator
        fun forValue(value: kotlin.String): ProcessingStatusDto {
                return values().first{it -> it.value == value}
        }
    }
}

