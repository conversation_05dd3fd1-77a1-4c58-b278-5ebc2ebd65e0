package com.klosesoft.billingsolution.domain.logic.service

import com.klosesoft.billingsolution.persistence.model.entity.Subscription
import com.klosesoft.billingsolution.persistence.service.SubscriptionItemLoadService
import kotlinx.coroutines.reactor.awaitSingle
import org.springframework.data.domain.Pageable
import org.springframework.data.r2dbc.core.R2dbcEntityTemplate
import org.springframework.stereotype.Service
import java.math.BigDecimal
import java.util.UUID

@Service
class SubscriptionSumUpdateService(
    private val subscriptionItemLoadService: SubscriptionItemLoadService,
    private val r2dbcEntityTemplate: R2dbcEntityTemplate,
    private val taxService: TaxService,
) {
    suspend fun updateTotalAmounts(
        subscription: Subscription,
        userId: UUID,
    ) {
        val subscriptionItems = subscriptionItemLoadService.findAllByTenantIdAndSubscriptionId(
            tenantId = subscription.tenantId,
            subscriptionId = subscription.id,
            rsqlFilter = null,
            pageable = Pageable.unpaged(),
        ).content

        val totalAmount = subscriptionItems.sumOf { item ->
            if (item.debitCreditIndicator.isDebit) item.totalNetAmount else -item.totalNetAmount
        }

        val updatedSubscription = subscription.copy(
            amount = totalAmount,
            lastModifiedBy = userId,
        )

        r2dbcEntityTemplate.update(updatedSubscription).awaitSingle()
    }
}
