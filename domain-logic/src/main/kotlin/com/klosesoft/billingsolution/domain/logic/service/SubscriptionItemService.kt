package com.klosesoft.billingsolution.domain.logic.service

import com.klosesoft.billingsolution.common.utils.BillingSolutionConstants
import com.klosesoft.billingsolution.domain.model.dto.SubscriptionItemRequestDomainDto
import com.klosesoft.billingsolution.persistence.model.entity.Subscription
import com.klosesoft.billingsolution.persistence.model.entity.SubscriptionItem
import com.klosesoft.billingsolution.persistence.service.SubscriptionItemLoadService
import com.klosesoft.billingsolution.persistence.service.SupplierLoadService
import kotlinx.coroutines.reactor.awaitSingle
import org.springframework.data.r2dbc.core.R2dbcEntityTemplate
import org.springframework.stereotype.Service
import java.util.UUID

@Service
class SubscriptionItemService(
    private val subscriptionItemLoadService: SubscriptionItemLoadService,
    private val r2dbcEntityTemplate: R2dbcEntityTemplate,
    private val taxService: TaxService,
    private val supplierLoadService: SupplierLoadService,
    private val subscriptionLoadService: SubscriptionLoadService,
    private val subscriptionSumUpdateService: SubscriptionSumUpdateService,
) {
    suspend fun addSubscriptionItem(
        userId: UUID,
        subscription: Subscription,
        subscriptionItemRequestDomainDto: SubscriptionItemRequestDomainDto,
        withoutUpdateAmounts: Boolean = false,
    ) {
        val subscriptionItem = toDomain(subscriptionItemRequestDomainDto, subscription, userId)

        r2dbcEntityTemplate.insert(subscriptionItem).awaitSingle()

        if (!withoutUpdateAmounts) {
            subscriptionSumUpdateService.updateTotalAmounts(subscription, BillingSolutionConstants.SYSTEM_USER_ID)
        }
    }

    suspend fun updateSubscriptionItem(
        userId: UUID,
        subscription: Subscription,
        subscriptionItemRequestDomainDto: SubscriptionItemRequestDomainDto,
        withoutUpdateAmounts: Boolean = false,
    ) {
        val existingSubscriptionItem = subscriptionItemLoadService.findByTenantIdAndSubscriptionIdAndKey(
            subscription.tenantId,
            subscription.id,
            subscriptionItemRequestDomainDto.key,
        )

        val updatedSubscriptionItem = toDomain(subscriptionItemRequestDomainDto, subscription, userId)
            .copy(
                id = existingSubscriptionItem.id,
                createdAt = existingSubscriptionItem.createdAt,
                createdBy = existingSubscriptionItem.createdBy,
                version = subscriptionItemRequestDomainDto.version,
            )

        r2dbcEntityTemplate.update(updatedSubscriptionItem).awaitSingle()

        if (!withoutUpdateAmounts) {
            subscriptionSumUpdateService.updateTotalAmounts(subscription, BillingSolutionConstants.SYSTEM_USER_ID)
        }
    }

    suspend fun deleteSubscriptionItem(
        subscription: Subscription,
        subscriptionItemKey: String,
        userId: UUID,
        withoutUpdateAmounts: Boolean = false,
    ) {
        val subscriptionItem = subscriptionItemLoadService.findByTenantIdAndSubscriptionIdAndKey(
            subscription.tenantId,
            subscription.id,
            subscriptionItemKey,
        )

        r2dbcEntityTemplate.delete(subscriptionItem).awaitSingle()

        if (!withoutUpdateAmounts) {
            subscriptionSumUpdateService.updateTotalAmounts(subscription, BillingSolutionConstants.SYSTEM_USER_ID)
        }
    }

    private suspend fun toDomain(
        subscriptionItemRequestDomainDto: SubscriptionItemRequestDomainDto,
        subscription: Subscription,
        userId: UUID,
    ): SubscriptionItem {
        val supplierId = subscriptionItemRequestDomainDto.supplierKey?.let { supplierKey ->
            supplierLoadService.findByTenantIdAndKey(subscription.tenantId, supplierKey).id
        }

        val taxes = taxService.toDomain(subscriptionItemRequestDomainDto.taxes)
        val totalNetAmount = taxService.calculateTotalNetAmount(
            subscriptionItemRequestDomainDto.quantity,
            subscriptionItemRequestDomainDto.unitNetAmount,
            subscriptionItemRequestDomainDto.debitCreditIndicator,
        )

        return SubscriptionItem(
            subscriptionId = subscription.id,
            articleNumber = subscriptionItemRequestDomainDto.articleNumber,
            category = subscriptionItemRequestDomainDto.category,
            itemGroup = subscriptionItemRequestDomainDto.itemGroup,
            supplierId = supplierId,
            name = subscriptionItemRequestDomainDto.name,
            description = subscriptionItemRequestDomainDto.description,
            quantity = subscriptionItemRequestDomainDto.quantity,
            currency = subscriptionItemRequestDomainDto.currency,
            unitNetAmount = subscriptionItemRequestDomainDto.unitNetAmount,
            properties = subscriptionItemRequestDomainDto.properties,
            debitCreditIndicator = subscriptionItemRequestDomainDto.debitCreditIndicator,
            taxes = taxes,
            totalNetAmount = totalNetAmount,
            key = subscriptionItemRequestDomainDto.key,
            tenantId = subscription.tenantId,
            createdBy = userId,
            lastModifiedBy = userId,
        )
    }
}
