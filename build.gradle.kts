import io.gitlab.arturbosch.detekt.Detekt
import io.gitlab.arturbosch.detekt.DetektCreateBaselineTask
import org.jetbrains.kotlin.gradle.dsl.JvmTarget
import org.jetbrains.kotlin.gradle.dsl.KotlinVersion
import org.jetbrains.kotlin.gradle.tasks.KotlinCompile

plugins {

    base
    alias(libs.plugins.kotlin.jvm) apply false
    alias(libs.plugins.kotlin.spring) apply false

    alias(libs.plugins.spring.boot) apply false
    alias(libs.plugins.spring.dependency.management) apply false

    alias(libs.plugins.gradle.docker.compose) apply false

    alias(libs.plugins.test.logger)
    alias(libs.plugins.gradle.ktlint)
    alias(libs.plugins.openapi.generator)
    alias(libs.plugins.detekt) apply false

    alias(libs.plugins.versions.plugin)
}

group = "com.klosesoft"
version = "0.0.1-SNAPSHOT"

subprojects {
    if (project.name != "app-webportal") {
        apply(plugin = "org.jetbrains.kotlin.jvm")
        apply(plugin = "org.jetbrains.kotlin.plugin.spring")
        apply(plugin = "io.spring.dependency-management")
        apply(plugin = "com.adarshr.test-logger")
        apply(plugin = "org.jlleitschuh.gradle.ktlint")
        apply(plugin = "io.gitlab.arturbosch.detekt")

        testlogger {
            setTheme("mocha")
        }

        ktlint {
            filter {
                exclude { element ->
                    element.file.path.contains("generated")
                }
            }
        }

        tasks.withType<Detekt>().configureEach {
            buildUponDefaultConfig = true // preconfigure defaults
            allRules = false // activate all available (even unstable) rules.
            config = files("../detekt.yml")
            jvmTarget = "21"
            autoCorrect = true
            exclude("**/generated/**", "**/databasefiller/**", "**/openapitools/**")
        }
        tasks.withType<DetektCreateBaselineTask>().configureEach {
            jvmTarget = "21"
        }

        tasks.withType<JavaCompile> {
            sourceCompatibility = "21"
            targetCompatibility = "21"
        }

        tasks.withType<KotlinCompile> {
            compilerOptions {
                jvmTarget.set(JvmTarget.JVM_21)
                apiVersion.set(KotlinVersion.KOTLIN_1_9)
                languageVersion.set(KotlinVersion.KOTLIN_1_9)
            }
        }

        tasks.withType<Jar> {
            isPreserveFileTimestamps = false
            isReproducibleFileOrder = true
        }

        tasks.withType<Test>().configureEach {
            useJUnitPlatform()
            jvmArgs("-XX:+EnableDynamicAgentLoading")
        }

        tasks.withType<Javadoc>().configureEach {
            options.encoding = "UTF-8"
        }

        configurations {
            all {
                exclude(group = "commons-logging", module = "commons-logging")
            }

            // TODO: exclude junit-jupiter-api and assertj-core from implementation
//            "implementation" {
//                exclude(group = "org.junit.jupiter", module = "junit-jupiter-api")
//                exclude(group = "org.assertj", module = "assertj-core")
//            }
        }

        tasks.withType<org.springframework.boot.gradle.tasks.bundling.BootJar> {
            enabled = false
        }
    }
}

tasks.register<Delete>("cleanApis") {
    delete("api-external-client/src/main/kotlin")
    delete("api-external-server/src/main/kotlin")
    delete("api-outgoing-notification-client/src/main/kotlin")
    delete("api-outgoing-notification-server/src/main/kotlin")
    delete("api-webportal-server/src/main/kotlin")
    delete("app-webportal/src/app/backend-api")
    delete("demo-shop/src/app/webportal-api")
}

tasks {
    val generateApis by registering {
        dependsOn("cleanApis")

        finalizedBy(
            ":api-volt-client:openApiGenerateVoltClient",
            ":api-external-client:openApiGenerateExternalClient",
            ":api-external-server:openApiGenerateExternal",
            ":api-outgoing-notification-client:openApiGenerateOutgoingNotificationClient",
            ":api-outgoing-notification-server:openApiGenerateOutgoingNotification",
            ":api-webportal-server:openApiGenerateWebPortal",
            ":app-webportal:openApiGenerateWebPortalClient",
            ":app-webportal-primeng:openApiGenerateWebPortalPrimengClient",
            ":demo-shop:openApiGenerateDemoShopClient",
        )
    }
}
