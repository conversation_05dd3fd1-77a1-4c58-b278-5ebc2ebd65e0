/**
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech) (7.8.0).
 * https://openapi-generator.tech
 * Do not edit the class manually.
*/
package com.klosesoft.billingsolution.generated.api.external.server

import com.klosesoft.billingsolution.generated.api.external.server.model.ExternalSubscriptionDetailsDataDto
import com.klosesoft.billingsolution.generated.api.external.server.model.ExternalSubscriptionDetailsResponseDto
import com.klosesoft.billingsolution.generated.api.external.server.model.ExternalSubscriptionItemRequestDto
import com.klosesoft.billingsolution.generated.api.external.server.model.ExternalSubscriptionItemResponseDto
import com.klosesoft.billingsolution.generated.api.external.server.model.ExternalSubscriptionItemsPagedResponseDto
import com.klosesoft.billingsolution.generated.api.external.server.model.ExternalSubscriptionsPagedResponseDto
import org.springframework.http.HttpStatus
import org.springframework.http.MediaType
import org.springframework.http.ResponseEntity

import org.springframework.web.bind.annotation.*
import org.springframework.validation.annotation.Validated
import org.springframework.web.context.request.NativeWebRequest
import org.springframework.beans.factory.annotation.Autowired

import jakarta.validation.constraints.DecimalMax
import jakarta.validation.constraints.DecimalMin
import jakarta.validation.constraints.Email
import jakarta.validation.constraints.Max
import jakarta.validation.constraints.Min
import jakarta.validation.constraints.NotNull
import jakarta.validation.constraints.Pattern
import jakarta.validation.constraints.Size
import jakarta.validation.Valid

import kotlinx.coroutines.flow.Flow
import kotlin.collections.List
import kotlin.collections.Map

@RestController
@Validated
interface SubscriptionsApi {


    @RequestMapping(
            method = [RequestMethod.POST],
            value = ["/external/v1/subscriptions/{subscription_key}/subscription-items"],
            consumes = ["application/json"]
    )
    suspend fun addSubscriptionItem( @PathVariable("subscription_key") subscriptionKey: kotlin.String, @Valid @RequestBody externalSubscriptionItemRequestDto: ExternalSubscriptionItemRequestDto): ResponseEntity<Unit>


    @RequestMapping(
            method = [RequestMethod.POST],
            value = ["/external/v1/subscriptions"],
            produces = ["application/json"],
            consumes = ["application/json"]
    )
    suspend fun createSubscription( @Valid @RequestBody externalSubscriptionDetailsDataDto: ExternalSubscriptionDetailsDataDto): ResponseEntity<ExternalSubscriptionDetailsResponseDto>


    @RequestMapping(
            method = [RequestMethod.DELETE],
            value = ["/external/v1/subscriptions/{subscription_key}"]
    )
    suspend fun deleteSubscription( @PathVariable("subscription_key") subscriptionKey: kotlin.String): ResponseEntity<Unit>


    @RequestMapping(
            method = [RequestMethod.DELETE],
            value = ["/external/v1/subscriptions/{subscription_key}/subscription-items/{subscription_item_key}"]
    )
    suspend fun deleteSubscriptionItem( @PathVariable("subscription_key") subscriptionKey: kotlin.String, @PathVariable("subscription_item_key") subscriptionItemKey: kotlin.String): ResponseEntity<Unit>


    @RequestMapping(
            method = [RequestMethod.GET],
            value = ["/external/v1/subscriptions/{subscription_key}"],
            produces = ["application/json"]
    )
    suspend fun loadSubscription( @PathVariable("subscription_key") subscriptionKey: kotlin.String): ResponseEntity<ExternalSubscriptionDetailsResponseDto>


    @RequestMapping(
            method = [RequestMethod.GET],
            value = ["/external/v1/subscriptions/{subscription_key}/subscription-items/{subscription_item_key}"],
            produces = ["application/json"]
    )
    suspend fun loadSubscriptionItem( @PathVariable("subscription_key") subscriptionKey: kotlin.String, @PathVariable("subscription_item_key") subscriptionItemKey: kotlin.String): ResponseEntity<ExternalSubscriptionItemResponseDto>


    @RequestMapping(
            method = [RequestMethod.GET],
            value = ["/external/v1/subscriptions/{subscription_key}/subscription-items"],
            produces = ["application/json"]
    )
    suspend fun loadSubscriptionItems( @PathVariable("subscription_key") subscriptionKey: kotlin.String,@NotNull  @Valid @RequestParam(value = "page", required = true) page: kotlin.Int,@NotNull  @Valid @RequestParam(value = "size", required = true) size: kotlin.Int, @Valid @RequestParam(value = "sort", required = false) sort: kotlin.String?, @Valid @RequestParam(value = "direction", required = false) direction: kotlin.String?, @Valid @RequestParam(value = "filter", required = false) filter: kotlin.String?): ResponseEntity<ExternalSubscriptionItemsPagedResponseDto>


    @RequestMapping(
            method = [RequestMethod.GET],
            value = ["/external/v1/subscriptions"],
            produces = ["application/json"]
    )
    suspend fun loadSubscriptions( @Valid @RequestParam(value = "page", required = false) page: kotlin.Int?, @Valid @RequestParam(value = "size", required = false) size: kotlin.Int?, @Valid @RequestParam(value = "sort", required = false) sort: kotlin.String?, @Valid @RequestParam(value = "direction", required = false) direction: kotlin.String?, @Valid @RequestParam(value = "filter", required = false) filter: kotlin.String?): ResponseEntity<ExternalSubscriptionsPagedResponseDto>


    @RequestMapping(
            method = [RequestMethod.PUT],
            value = ["/external/v1/subscriptions/{subscription_key}"],
            produces = ["application/json"],
            consumes = ["application/json"]
    )
    suspend fun updateSubscription( @PathVariable("subscription_key") subscriptionKey: kotlin.String, @Valid @RequestBody externalSubscriptionDetailsDataDto: ExternalSubscriptionDetailsDataDto): ResponseEntity<ExternalSubscriptionDetailsResponseDto>


    @RequestMapping(
            method = [RequestMethod.PUT],
            value = ["/external/v1/subscriptions/{subscription_key}/subscription-items/{subscription_item_key}"],
            consumes = ["application/json"]
    )
    suspend fun updateSubscriptionItem( @PathVariable("subscription_key") subscriptionKey: kotlin.String, @PathVariable("subscription_item_key") subscriptionItemKey: kotlin.String, @Valid @RequestBody externalSubscriptionItemRequestDto: ExternalSubscriptionItemRequestDto): ResponseEntity<Unit>
}
