package com.klosesoft.billingsolution.generated.api.external.server.model

import java.util.Objects
import com.fasterxml.jackson.annotation.JsonCreator
import com.fasterxml.jackson.annotation.JsonProperty
import com.fasterxml.jackson.annotation.JsonValue
import com.klosesoft.billingsolution.generated.api.external.server.model.CurrencyDto
import com.klosesoft.billingsolution.generated.api.external.server.model.DebitCreditIndicatorDto
import com.klosesoft.billingsolution.generated.api.external.server.model.ItemCategoryDto
import com.klosesoft.billingsolution.generated.api.external.server.model.PropertyDto
import com.klosesoft.billingsolution.generated.api.external.server.model.TaxInformationDto
import jakarta.validation.constraints.DecimalMax
import jakarta.validation.constraints.DecimalMin
import jakarta.validation.constraints.Email
import jakarta.validation.constraints.Max
import jakarta.validation.constraints.Min
import jakarta.validation.constraints.NotNull
import jakarta.validation.constraints.Pattern
import jakarta.validation.constraints.Size
import jakarta.validation.Valid

/**
 * 
 * @param key The key of the subscription item
 * @param version The version of the subscription item
 * @param articleNumber The article number of the subscription item
 * @param category 
 * @param debitCreditIndicator 
 * @param name The name of the subscription item
 * @param quantity The quantity of the subscription item
 * @param unitNetAmount The unit net amount of the subscription item
 * @param totalNetAmount The total net amount of the subscription item
 * @param currency 
 * @param taxes 
 * @param itemGroup The item group of the subscription item
 * @param supplierKey The supplier key of the subscription item
 * @param description The description of the subscription item
 * @param properties 
 */
data class ExternalSubscriptionItemResponseDto(

    @get:JsonProperty("key", required = true) val key: kotlin.String,

    @get:JsonProperty("version", required = true) val version: kotlin.Long,

    @get:JsonProperty("articleNumber", required = true) val articleNumber: kotlin.String,

    @field:Valid
    @get:JsonProperty("category", required = true) val category: ItemCategoryDto,

    @field:Valid
    @get:JsonProperty("debitCreditIndicator", required = true) val debitCreditIndicator: DebitCreditIndicatorDto,

    @get:JsonProperty("name", required = true) val name: kotlin.String,

    @get:JsonProperty("quantity", required = true) val quantity: java.math.BigDecimal,

    @get:JsonProperty("unitNetAmount", required = true) val unitNetAmount: java.math.BigDecimal,

    @get:JsonProperty("totalNetAmount", required = true) val totalNetAmount: java.math.BigDecimal,

    @field:Valid
    @get:JsonProperty("currency", required = true) val currency: CurrencyDto,

    @field:Valid
    @get:JsonProperty("taxes", required = true) val taxes: kotlin.collections.List<TaxInformationDto>,

    @get:JsonProperty("itemGroup") val itemGroup: kotlin.String? = null,

    @get:JsonProperty("supplierKey") val supplierKey: kotlin.String? = null,

    @get:JsonProperty("description") val description: kotlin.String? = null,

    @field:Valid
    @get:JsonProperty("properties") val properties: kotlin.collections.List<PropertyDto>? = null
    ) {

}

