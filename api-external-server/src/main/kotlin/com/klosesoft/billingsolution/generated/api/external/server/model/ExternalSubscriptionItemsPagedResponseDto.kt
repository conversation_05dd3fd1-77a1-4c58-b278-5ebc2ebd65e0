package com.klosesoft.billingsolution.generated.api.external.server.model

import java.util.Objects
import com.fasterxml.jackson.annotation.JsonProperty
import com.klosesoft.billingsolution.generated.api.external.server.model.ExternalSubscriptionItemResponseDto
import com.klosesoft.billingsolution.generated.api.external.server.model.PageDataDto
import jakarta.validation.constraints.DecimalMax
import jakarta.validation.constraints.DecimalMin
import jakarta.validation.constraints.Email
import jakarta.validation.constraints.Max
import jakarta.validation.constraints.Min
import jakarta.validation.constraints.NotNull
import jakarta.validation.constraints.Pattern
import jakarta.validation.constraints.Size
import jakarta.validation.Valid

/**
 * 
 * @param pageData 
 * @param items 
 */
data class ExternalSubscriptionItemsPagedResponseDto(

    @field:Valid
    @get:JsonProperty("pageData", required = true) val pageData: PageDataDto,

    @field:Valid
    @get:JsonProperty("items", required = true) val items: kotlin.collections.List<ExternalSubscriptionItemResponseDto>
    ) {

}

